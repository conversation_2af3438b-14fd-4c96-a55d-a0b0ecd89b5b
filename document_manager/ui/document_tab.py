"""Document Manager tab implementation.

This module provides the main tab for the Document Manager pillar.
"""

import random

from loguru import logger
from PyQt6.QtCore import QPointF, Qt
from PyQt6.QtGui import (
    QBrush,
    QColor,
    QLinearGradient,
    QPainter,
    QPainterPath,
    QPen,
    QPixmap,
)
from PyQt6.QtWidgets import (
    QFrame,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QVBoxLayout,
    QWidget,
)

from shared.ui.window_management import Tab<PERSON>anager, WindowManager


class DocumentTab(QWidget):
    """Main tab for the Document Manager pillar."""

    def __init__(self, tab_manager: TabManager, window_manager: WindowManager) -> None:
        """Initialize the Document Manager tab.

        Args:
            tab_manager: Application tab manager
            window_manager: Application window manager
        """
        super().__init__()
        self.tab_manager = tab_manager
        self.window_manager = window_manager

        # Background properties
        self.parchment_noise = []
        self.generate_parchment_texture()

        # Initialize UI
        self._init_ui()
        logger.debug("DocumentTab initialized")

    def generate_parchment_texture(self):
        """Generate parchment texture pattern."""
        # Generate random noise for parchment texture
        for _ in range(400):  # Doubled for stronger pattern
            x = random.randint(0, 100)  # As percentage of width
            y = random.randint(0, 100)  # As percentage of height
            size = random.uniform(0.5, 4)  # Increased size
            opacity = random.uniform(0.05, 0.15)  # Stronger noise

            # More varied shades of brown
            hue = random.randint(20, 45)  # Wider range of brown-yellow
            saturation = random.randint(30, 70)  # More saturation
            lightness = random.randint(25, 65)  # More contrast

            self.parchment_noise.append(
                {
                    "x": x,
                    "y": y,
                    "size": size,
                    "opacity": opacity,
                    "hue": hue,
                    "saturation": saturation,
                    "lightness": lightness,
                }
            )

        # Add some fibrous horizontal lines for papyrus effect
        for _ in range(30):
            y = random.randint(0, 100)
            length = random.uniform(10, 40)  # Length of fiber
            thickness = random.uniform(0.5, 2)
            fiber_opacity = random.uniform(0.1, 0.2)

            self.parchment_noise.append(
                {
                    "x": random.randint(0, 100 - int(length)),
                    "y": y,
                    "size": thickness,
                    "length": length,
                    "opacity": fiber_opacity,
                    "hue": random.randint(25, 35),
                    "saturation": random.randint(40, 60),
                    "lightness": random.randint(30, 50),
                    "is_fiber": True,
                }
            )

    def _init_ui(self) -> None:
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Content container with background styling
        content_container = QWidget()
        content_container.setObjectName("document_content")
        content_container.setStyleSheet(
            """
            QWidget#document_content {
                background-color: transparent;
            }
        """
        )
        content_layout = QVBoxLayout(content_container)

        # Button bar
        button_bar = QWidget()
        button_layout = QHBoxLayout(button_bar)
        button_layout.setContentsMargins(5, 5, 5, 5)
        button_layout.setSpacing(5)

        # Document Browser button
        doc_browser_btn = QPushButton("Document Browser")
        doc_browser_btn.setToolTip("Open Document Browser")
        doc_browser_btn.clicked.connect(self._open_document_browser)
        button_layout.addWidget(doc_browser_btn)

        # Document Analysis button
        analysis_btn = QPushButton("Document Analysis")
        analysis_btn.setToolTip("Analyze documents from a gematric perspective")
        analysis_btn.clicked.connect(self._open_document_analysis)
        button_layout.addWidget(analysis_btn)

        # RTF Editor button
        rtf_btn = QPushButton("RTF Editor")
        rtf_btn.setToolTip("Open Rich Text Editor")
        rtf_btn.clicked.connect(self._open_rtf_editor)
        button_layout.addWidget(rtf_btn)

        # Document Database Manager button
        db_manager_btn = QPushButton("Database Manager")
        db_manager_btn.setToolTip("Manage documents in the database")
        db_manager_btn.clicked.connect(self._open_document_database_manager)
        button_layout.addWidget(db_manager_btn)

        # Advanced Search button
        search_btn = QPushButton("Advanced Search")
        search_btn.setToolTip("Advanced search with phrase matching and position tracking")
        search_btn.clicked.connect(self._open_advanced_search)
        button_layout.addWidget(search_btn)

        # Convert & Save button
        convert_save_btn = QPushButton("Convert & Save")
        convert_save_btn.setToolTip("Convert documents and save them to the database")
        convert_save_btn.clicked.connect(self._open_convert_and_save)
        button_layout.addWidget(convert_save_btn)

        # Document Conversion button
        conversion_btn = QPushButton("Document Conversion")
        conversion_btn.setToolTip("Convert documents (PDF, DOCX, etc.) to RTF format")
        conversion_btn.clicked.connect(self._open_document_conversion)
        button_layout.addWidget(conversion_btn)

        # KWIC Concordance button
        concordance_btn = QPushButton("KWIC Concordance")
        concordance_btn.setToolTip("Create and manage Key Word In Context concordances")
        concordance_btn.clicked.connect(self._open_kwic_concordance)
        button_layout.addWidget(concordance_btn)

        # Gematria Dictionary button
        gematria_dict_btn = QPushButton("Gematria Dictionary")
        gematria_dict_btn.setToolTip("Analyze documents and create gematria dictionaries")
        gematria_dict_btn.clicked.connect(self._open_gematria_dictionary)
        button_layout.addWidget(gematria_dict_btn)

        # Knowledge Base button
        knowledge_base_btn = QPushButton("Knowledge Base")
        knowledge_base_btn.setToolTip("Manage Knowledge Base documents and import converted documents")
        knowledge_base_btn.clicked.connect(self._open_knowledge_base)
        button_layout.addWidget(knowledge_base_btn)

        # Help button (right-aligned)
        help_btn = QPushButton("Help")
        help_btn.setToolTip("Show Document Manager help")
        help_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """
        )
        # help_btn.clicked.connect(self._show_help)
        button_layout.addWidget(help_btn)

        # Add stretch to push buttons to the left
        button_layout.addStretch()

        # Add button bar to content layout
        content_layout.addWidget(button_bar)

        # Create card for welcome content
        welcome_card = QFrame()
        welcome_card.setObjectName("welcomeCard")
        welcome_card.setStyleSheet(
            """
            #welcomeCard {
                background-color: rgba(255, 255, 235, 0.85);
                border-radius: 8px;
                border: 1px solid #e0d0b0;
                padding: 15px;
                margin: 20px 40px;
                min-width: 500px;
                max-width: 800px;
                min-height: 200px;
            }
        """
        )
        welcome_layout = QVBoxLayout(welcome_card)
        welcome_layout.setContentsMargins(15, 15, 15, 15)
        welcome_layout.setSpacing(10)

        # Title and welcome message with enhanced styling
        title = QLabel("Document Manager")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #795548;")

        welcome = QLabel(
            "Welcome to the Document Manager pillar. Here you can organize, analyze, "
            "and edit documents using various tools and techniques."
        )
        welcome.setWordWrap(True)
        welcome.setStyleSheet("font-size: 14px; color: #555;")
        welcome.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Description with more details
        description = QLabel(
            "The Document Manager provides tools for organizing your research materials, "
            "performing textual analysis on documents, and creating rich-text documents "
            "with embedded references to your gematric findings."
        )
        description.setWordWrap(True)
        description.setStyleSheet("font-size: 12px; color: #777; margin-top: 10px;")
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Add content to welcome card
        welcome_layout.addWidget(title, alignment=Qt.AlignmentFlag.AlignCenter)
        welcome_layout.addWidget(welcome)
        welcome_layout.addWidget(description)

        # Add welcome card to content layout
        content_layout.addWidget(welcome_card, alignment=Qt.AlignmentFlag.AlignCenter)

        # Add Seshat image below welcome card
        seshat_container = QFrame()
        seshat_container.setObjectName("seshatImageContainer")
        seshat_container.setStyleSheet(
            """
            #seshatImageContainer {
                background-color: rgba(255, 255, 235, 0.7);
                border-radius: 8px;
                margin: 10px 40px 20px 40px;
                padding: 10px;
            }
        """
        )
        seshat_layout = QVBoxLayout(seshat_container)

        # Create image label
        seshat_image_label = QLabel()
        seshat_pixmap = QPixmap("assets/tab_images/sechat.png")

        if not seshat_pixmap.isNull():
            # Scale the image to a reasonable size while maintaining aspect ratio
            scaled_pixmap = seshat_pixmap.scaled(
                400,
                400,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
            seshat_image_label.setPixmap(scaled_pixmap)
            seshat_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # Add caption
            caption = QLabel(
                "Seshat - Egyptian goddess of writing, record keeping, and archives"
            )
            caption.setStyleSheet(
                "font-size: 12px; color: #795548; font-style: italic; background-color: rgba(255, 255, 235, 0.8); padding: 4px; border-radius: 4px;"
            )
            caption.setAlignment(Qt.AlignmentFlag.AlignCenter)

            seshat_layout.addWidget(
                seshat_image_label, alignment=Qt.AlignmentFlag.AlignCenter
            )
            seshat_layout.addWidget(caption, alignment=Qt.AlignmentFlag.AlignCenter)

            # Add the image container to the content layout
            content_layout.addWidget(
                seshat_container, alignment=Qt.AlignmentFlag.AlignCenter
            )
        else:
            logger.error(
                "Failed to load Seshat image from assets/tab_images/sechat.png"
            )

        content_layout.addStretch()

        # Add the content container to the main layout
        layout.addWidget(content_container)

    def paintEvent(self, event):
        """Custom paint event to draw the parchment background."""
        # Call the parent class's paintEvent to handle basic widget drawing
        super().paintEvent(event)

        # Create a painter for this widget
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Draw parchment background
        self._draw_parchment_background(painter)

    def _draw_parchment_background(self, painter):
        """Draw the parchment texture background."""
        # Create a more defined parchment base color with gradient
        gradient = QLinearGradient(QPointF(0, 0), QPointF(self.width(), self.height()))
        gradient.setColorAt(0, QColor(255, 248, 220))  # Light parchment
        gradient.setColorAt(0.3, QColor(250, 240, 210))  # Mid tone
        gradient.setColorAt(0.7, QColor(245, 235, 200))  # Slightly darker
        gradient.setColorAt(1, QColor(240, 230, 190))  # Darker edge

        painter.fillRect(self.rect(), QBrush(gradient))

        # Draw more defined grid lines (like papyrus fibers)
        painter.setPen(QPen(QColor(150, 130, 100, 20), 1, Qt.PenStyle.SolidLine))

        # Horizontal lines (more dense)
        line_spacing = 15  # Decreased spacing
        for y in range(0, self.height(), line_spacing):
            painter.drawLine(0, y, self.width(), y)

        # Add some vertical fibers (less frequent)
        painter.setPen(QPen(QColor(140, 120, 90, 10), 1, Qt.PenStyle.SolidLine))
        line_spacing = 40
        for x in range(0, self.width(), line_spacing):
            # Slightly wavy vertical lines
            path = QPainterPath()
            path.moveTo(x, 0)

            segments = self.height() // 20
            for i in range(1, segments + 1):
                offset = random.uniform(-1.5, 1.5)
                path.lineTo(x + offset, i * 20)

            painter.drawPath(path)

        # Draw noise spots and fibers for texture
        for spot in self.parchment_noise:
            x = int(self.width() * spot["x"] / 100)
            y = int(self.height() * spot["y"] / 100)

            # Create a color with alpha for the noise
            color = QColor()
            color.setHsv(
                spot["hue"],
                spot["saturation"],
                spot["lightness"],
                int(255 * spot["opacity"]),
            )

            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QBrush(color))

            if spot.get("is_fiber", False):
                # Draw a horizontal fiber for papyrus texture
                fiber_width = int(self.width() * spot["length"] / 100)
                fiber_height = int(spot["size"])  # Convert to int for drawRect
                painter.drawRect(x, y, fiber_width, fiber_height)
            else:
                # Draw a small ellipse for the noise spot
                painter.drawEllipse(QPointF(x, y), spot["size"], spot["size"])

    def _open_document_browser(self) -> None:
        """Open the Document Browser window."""
        from document_manager.ui.panels.document_manager_panel import (
            DocumentManagerPanel,
        )

        # Create the panel
        panel = DocumentManagerPanel()

        # Create and open the document browser window
        self.window_manager.open_window("document_browser", panel)

        # Set the window title
        panel.setWindowTitle("Document Browser")

        logger.debug("Opened Document Browser window")

    def _open_document_analysis(self) -> None:
        """Open the Document Analysis window."""
        from document_manager.ui.panels.document_analysis_panel import (
            DocumentAnalysisPanel,
        )

        # Create the panel
        panel = DocumentAnalysisPanel()

        # Create and open the document analysis window
        self.window_manager.open_window("document_analysis", panel)

        # Set the window title
        panel.setWindowTitle("Document Analysis")

        logger.debug("Opened Document Analysis window")

    def _open_rtf_editor(self) -> None:
        """Open the RTF Editor window."""
        from loguru import logger

        from shared.ui.widgets.rtf_editor.rtf_editor_window import RTFEditorWindow

        logger.debug("Opening RTF Editor window directly...")

        # Create the RTF editor window
        editor = RTFEditorWindow()

        # Open the window using the window manager
        self.window_manager.open_window("rtf_editor", editor)

        # Set the window title
        editor.setWindowTitle("Rich Text Editor")

        logger.debug("RTF Editor window opened")

    def _open_document_database_manager(self) -> None:
        """Open the Document Database Utility window."""
        from document_manager.ui.panels.document_database_utility_panel import (
            DocumentDatabaseUtilityPanel,
        )

        # Create the panel
        panel = DocumentDatabaseUtilityPanel()

        # Create and open the document database utility window
        self.window_manager.open_window("document_database_utility", panel)

        # Set the window title
        panel.setWindowTitle("Document Database Utility")

        logger.debug("Opened Document Database Utility window")

    def _open_advanced_search(self) -> None:
        """Open the Advanced Document Search window."""
        from document_manager.ui.panels.advanced_document_search_panel import (
            AdvancedDocumentSearchPanel,
        )

        # Create the panel
        panel = AdvancedDocumentSearchPanel()

        # Create and open the advanced search window
        self.window_manager.open_window("advanced_document_search", panel)

        # Set the window title
        panel.setWindowTitle("Advanced Document Search")

        logger.debug("Opened Advanced Document Search window")

    def _open_convert_and_save(self) -> None:
        """Open the Convert and Save Documents dialog."""
        from document_manager.ui.dialogs.convert_and_save_dialog import (
            ConvertAndSaveDialog,
        )

        # Create and show the dialog
        dialog = ConvertAndSaveDialog(self)
        dialog.exec()

        logger.debug("Opened Convert and Save Documents dialog")

    def _open_kwic_concordance(self) -> None:
        """Open the KWIC Concordance window."""
        from document_manager.ui.panels.concordance_panel import ConcordancePanel

        # Create the panel
        panel = ConcordancePanel()

        # Create and open the KWIC concordance window
        self.window_manager.open_window("kwic_concordance", panel)

        # Set the window title
        panel.setWindowTitle("KWIC Concordance")

        logger.debug("Opened KWIC Concordance window")

    def _open_gematria_dictionary(self) -> None:
        """Open the Gematria Dictionary window."""
        from document_manager.ui.panels.gematria_dictionary_panel import (
            GematriaDictionaryPanel,
        )

        # Create the panel
        panel = GematriaDictionaryPanel()

        # Create and open the Gematria Dictionary window
        self.window_manager.open_window("gematria_dictionary", panel)

        # Set the window title
        panel.setWindowTitle("Gematria Dictionary")

        logger.debug("Opened Gematria Dictionary window")

    def _open_knowledge_base(self) -> None:
        """Open the Knowledge Base window."""
        try:
            from knowledge_base.ui.knowledge_base_panel import KnowledgeBasePanel

            # Create the panel
            panel = KnowledgeBasePanel()

            # Create and open the Knowledge Base window
            self.window_manager.open_window("knowledge_base", panel)

            # Set the window title
            panel.setWindowTitle("Knowledge Base")

            logger.debug("Opened Knowledge Base window")
        except ImportError as e:
            logger.error(f"Knowledge Base module not available: {e}")
            # Show a user-friendly error message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "Knowledge Base",
                "The Knowledge Base feature is not yet fully implemented.\n"
                "Please use the Document Database Manager for now."
            )
        except Exception as e:
            logger.error(f"Failed to open Knowledge Base window: {e}")
            # Also show a user-friendly error message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open Knowledge Base window:\n{str(e)}"
            )

    def _open_document_conversion(self) -> None:
        """Open the Document Conversion dialog."""
        from document_manager.services.document_conversion_service import DocumentConversionService
        from document_manager.ui.dialogs.document_conversion_dialog import DocumentConversionDialog

        try:
            # Create the conversion service (it creates its own DocumentService internally)
            conversion_service = DocumentConversionService()
            
            # Create and show the dialog with correct parameter order
            dialog = DocumentConversionDialog(parent=self, conversion_service=conversion_service)
            dialog.setWindowTitle("Document Conversion")
            
            # Connect the signal to handle converted documents
            dialog.documents_converted.connect(self._handle_documents_converted)
            
            # Show the dialog
            dialog.exec()
            
            logger.debug("Opened Document Conversion dialog")
        except Exception as e:
            logger.error(f"Failed to open Document Conversion dialog: {e}")
            # Also show a user-friendly error message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self, 
                "Error", 
                f"Failed to open Document Conversion dialog:\n{str(e)}"
            )

    def _handle_documents_converted(self, converted_documents) -> None:
        """Handle the documents_converted signal from the Document Conversion dialog.
        
        Opens the RTF Editor with the converted documents using safe loading techniques
        to prevent UI freezing with large documents.
        
        Args:
            converted_documents: List of DocumentFormat objects representing converted documents
        """
        try:
            from shared.ui.widgets.rtf_editor.rtf_editor_window import RTFEditorWindow
            from PyQt6.QtWidgets import QMessageBox
            import time
            import psutil
            import os

            # DEBUGGING: Start timing and memory monitoring
            start_time = time.time()
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            logger.debug(f"=== IMPORT DEBUG START ===")
            logger.debug(f"Handling {len(converted_documents)} converted documents")
            logger.debug(f"Initial memory usage: {initial_memory:.2f} MB")
            logger.debug(f"Process start time: {start_time}")

            if not converted_documents:
                logger.warning("No converted documents received")
                return

            # Create a new RTF Editor window for each converted document
            for i, document_format in enumerate(converted_documents):
                try:
                    doc_start_time = time.time()
                    current_memory = process.memory_info().rss / 1024 / 1024  # MB

                    logger.debug(f"--- DOCUMENT {i+1}/{len(converted_documents)} DEBUG ---")
                    logger.debug(f"Document name: {getattr(document_format, 'name', 'Unknown')}")
                    logger.debug(f"Document type: {type(document_format)}")
                    logger.debug(f"Document attributes: {[attr for attr in dir(document_format) if not attr.startswith('_')]}")
                    logger.debug(f"Memory before document {i+1}: {current_memory:.2f} MB")

                    # Check document content sizes
                    if hasattr(document_format, 'html_content') and document_format.html_content:
                        html_size = len(document_format.html_content)
                        logger.debug(f"HTML content size: {html_size:,} characters ({html_size/1024:.2f} KB)")

                    if hasattr(document_format, 'content') and document_format.content:
                        content_size = len(document_format.content)
                        logger.debug(f"Plain content size: {content_size:,} characters ({content_size/1024:.2f} KB)")

                    logger.debug(f"Creating RTF Editor window for: {document_format.name}")

                    # Create the RTF editor window
                    editor_create_start = time.time()
                    editor = RTFEditorWindow()
                    editor_create_time = time.time() - editor_create_start
                    logger.debug(f"RTF Editor creation took: {editor_create_time:.3f} seconds")

                    # Load the document safely to prevent UI freezing
                    load_start_time = time.time()
                    logger.debug(f"Starting safe document load for: {document_format.name}")

                    self._load_document_safely(editor, document_format)

                    load_time = time.time() - load_start_time
                    logger.debug(f"Document loading took: {load_time:.3f} seconds")

                    # Generate a unique window key for each document
                    window_key = f"rtf_editor_converted_{i}_{document_format.id if hasattr(document_format, 'id') else 'unknown'}"
                    logger.debug(f"Generated window key: {window_key}")

                    # Open the window using the window manager
                    window_open_start = time.time()
                    self.window_manager.open_window(window_key, editor)
                    window_open_time = time.time() - window_open_start
                    logger.debug(f"Window manager open took: {window_open_time:.3f} seconds")

                    # Set an appropriate window title
                    title = f"RTF Editor - {document_format.name}" if hasattr(document_format, 'name') and document_format.name else f"RTF Editor - Converted Document {i+1}"
                    editor.setWindowTitle(title)

                    doc_total_time = time.time() - doc_start_time
                    final_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_delta = final_memory - current_memory

                    logger.debug(f"Document {i+1} completed in: {doc_total_time:.3f} seconds")
                    logger.debug(f"Memory after document {i+1}: {final_memory:.2f} MB (delta: {memory_delta:+.2f} MB)")
                    logger.info(f"Successfully opened RTF Editor with converted document: {title}")

                except Exception as doc_error:
                    error_time = time.time()
                    logger.error(f"IMPORT ERROR at {error_time}: Failed to open individual document {i}: {doc_error}")
                    logger.error(f"Error occurred after {error_time - start_time:.3f} seconds from start")
                    logger.error(f"Document details: name={getattr(document_format, 'name', 'Unknown')}, type={type(document_format)}")
                    # Continue with other documents even if one fails
                    continue

            total_time = time.time() - start_time
            final_total_memory = process.memory_info().rss / 1024 / 1024  # MB
            total_memory_delta = final_total_memory - initial_memory

            logger.debug(f"=== IMPORT DEBUG COMPLETE ===")
            logger.debug(f"Total import time: {total_time:.3f} seconds")
            logger.debug(f"Final memory usage: {final_total_memory:.2f} MB")
            logger.debug(f"Total memory delta: {total_memory_delta:+.2f} MB")
            logger.debug(f"Successfully processed {len(converted_documents)} documents")

        except Exception as e:
            error_time = time.time()
            logger.error(f"CRITICAL IMPORT ERROR at {error_time}: Failed to open RTF Editor with converted documents: {e}")
            if 'start_time' in locals():
                logger.error(f"Error occurred after {error_time - start_time:.3f} seconds from start")

            from PyQt6.QtWidgets import QMessageBox
            # Create error dialog with proper z-ordering
            error_box = QMessageBox(self)
            error_box.setWindowTitle("Error Opening RTF Editor")
            error_box.setText(f"Failed to open RTF Editor with converted documents:\n{str(e)}\n\n"
                             f"Check the logs for detailed debugging information.")
            error_box.setIcon(QMessageBox.Icon.Critical)
            error_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            error_box.setWindowFlags(error_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            error_box.exec()

    def _load_document_safely(self, editor, document_format, mode='html') -> None:
        """Load a document into the RTF Editor safely to prevent UI freezing.

        Args:
            editor: RTF Editor window instance
            document_format: DocumentFormat object to load
            mode: Loading mode - 'html', 'plain', or 'truncated'
        """
        from PyQt6.QtCore import QTimer
        import time
        import psutil
        import os

        # With 32GB+ RAM systems, we don't need aggressive size limits
        MAX_HTML_SIZE = 100000000   # 100MB of HTML - very generous for modern systems
        MAX_PLAIN_SIZE = 100000000  # 100MB of plain text - no practical limit

        # DEBUGGING: Start detailed load timing
        load_start_time = time.time()
        process = psutil.Process(os.getpid())
        load_initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        logger.debug(f"    === LOAD DOCUMENT DEBUG START ===")
        logger.debug(f"    Loading mode: {mode}")
        logger.debug(f"    Document name: {getattr(document_format, 'name', 'Unknown')}")
        logger.debug(f"    Editor type: {type(editor)}")
        logger.debug(f"    Load start memory: {load_initial_memory:.2f} MB")

        try:
            content = None
            content_size = 0

            # Validate document_format has required attributes
            validation_start = time.time()
            if not hasattr(document_format, 'name'):
                document_format.name = "Unknown Document"
            validation_time = time.time() - validation_start
            logger.debug(f"    Document validation took: {validation_time:.3f} seconds")

            if mode == 'html':
                html_mode_start = time.time()
                logger.debug(f"    Processing HTML mode...")

                # Try HTML content first
                if hasattr(document_format, 'html_content') and document_format.html_content:
                    content = document_format.html_content
                    content_size = len(content)
                    logger.debug(f"    Found HTML content: {content_size:,} characters ({content_size/1024:.2f} KB)")

                    # Validate HTML content before size check
                    validation_start = time.time()
                    if not self._is_valid_html_content(content):
                        validation_time = time.time() - validation_start
                        logger.warning(f"    Invalid HTML content detected after {validation_time:.3f}s, falling back to plain text")
                        return self._load_document_safely(editor, document_format, 'plain')
                    validation_time = time.time() - validation_start
                    logger.debug(f"    HTML validation took: {validation_time:.3f} seconds")

                    # With modern systems and generous limits, just load the content
                    # Only log if it's truly massive (over 100MB)
                    if content_size > MAX_HTML_SIZE:
                        logger.info(f"    Loading very large HTML content ({content_size:,} chars) - this may take a moment")
                        # No dialog needed - just load it

                    # Load HTML content safely
                    html_load_start = time.time()
                    logger.debug(f"    Loading HTML content safely...")
                    self._load_html_safely(editor, content)
                    html_load_time = time.time() - html_load_start
                    logger.debug(f"    HTML loading took: {html_load_time:.3f} seconds")

                    html_mode_time = time.time() - html_mode_start
                    logger.debug(f"    HTML mode completed in: {html_mode_time:.3f} seconds")
                    return
                else:
                    logger.debug(f"    No HTML content found, trying fallback methods")

            elif mode == 'plain':
                # Try plain text content with multiple fallback attributes
                content = self._extract_plain_text_content(document_format)
                if content:
                    content_size = len(content)

                    # With generous limits, just load the content
                    if content_size > MAX_PLAIN_SIZE:
                        logger.info(f"Very large plain text content ({content_size} chars) - loading anyway")
                        # No truncation needed with modern systems

                    # Load plain text directly (usually faster than HTML)
                    editor.text_edit.setPlainText(content)
                    logger.debug(f"Loaded document as plain text ({content_size} chars)")
                    return

            elif mode == 'truncated':
                # Load truncated version - this mode should rarely be used with new higher thresholds
                logger.warning(f"Loading document in truncated mode (this should be rare with new thresholds)")

                if hasattr(document_format, 'html_content') and document_format.html_content:
                    logger.debug(f"Truncating HTML content from {len(document_format.html_content)} to {MAX_HTML_SIZE} chars")
                    content = self._truncate_html_safely(document_format.html_content, MAX_HTML_SIZE)

                    # Debug the truncation result
                    logger.debug(f"Truncation result: {len(content)} characters")
                    if len(content) == 0:
                        logger.error("Truncation resulted in empty content! Falling back to plain text extraction.")
                        content = self._extract_plain_text_content(document_format)
                        if content:
                            content = content[:MAX_PLAIN_SIZE]
                            editor.text_edit.setPlainText(content + f"\n\n[Document converted to plain text - {len(content):,} characters shown]")
                        else:
                            editor.text_edit.setPlainText(f"[Error: Could not extract any content from document: {document_format.name}]")
                        return

                    truncation_notice = f"\n\n[Document truncated - showing first {len(content):,} characters of {len(document_format.html_content):,} total]"
                    self._load_html_safely(editor, content)
                    # Add truncation notice as plain text after a delay
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(100, lambda: editor.text_edit.append(truncation_notice))

                elif hasattr(document_format, 'content') and document_format.content:
                    content = document_format.content[:MAX_PLAIN_SIZE]
                    truncation_notice = f"\n\n[Document truncated - showing first {len(content):,} characters of {len(document_format.content):,} total]"
                    editor.text_edit.setPlainText(content + truncation_notice)
                else:
                    # Try to extract any available content
                    content = self._extract_plain_text_content(document_format)
                    if content:
                        content = content[:MAX_PLAIN_SIZE]
                        truncation_notice = f"\n\n[Document truncated - showing first {len(content):,} characters total]"
                        editor.text_edit.setPlainText(content + truncation_notice)
                    else:
                        editor.text_edit.setPlainText(f"[No content available for document: {document_format.name}]")
                        return

                logger.debug(f"Loaded truncated document ({len(content)} chars)")
                return

            # If no content was loaded by this point, try fallback methods
            logger.warning(f"No suitable content found for document {document_format.name}, trying fallback methods")

            # Try to extract any available content
            fallback_content = self._extract_plain_text_content(document_format)
            if fallback_content:
                # Limit fallback content size
                if len(fallback_content) > MAX_PLAIN_SIZE:
                    fallback_content = fallback_content[:MAX_PLAIN_SIZE] + "\n\n[Content truncated due to size]"
                editor.text_edit.setPlainText(fallback_content)
                logger.debug(f"Loaded document using fallback content extraction")
                return

            # If still no content, show empty document with message
            editor.text_edit.setPlainText(f"[No readable content found for document: {document_format.name}]")
            logger.warning(f"No readable content found for document: {document_format.name}")

        except Exception as e:
            error_time = time.time()
            load_error_memory = process.memory_info().rss / 1024 / 1024  # MB

            logger.error(f"    LOAD ERROR at {error_time}: Failed to load document safely: {e}")
            logger.error(f"    Load error memory: {load_error_memory:.2f} MB")
            logger.error(f"    Error occurred after {error_time - load_start_time:.3f} seconds from load start")

            # Last resort - show error message in editor
            try:
                fallback_start = time.time()
                editor.text_edit.setPlainText(f"[Error loading document: {str(e)}]")
                fallback_time = time.time() - fallback_start
                logger.debug(f"    Error message fallback took: {fallback_time:.3f} seconds")
            except Exception as final_error:
                logger.error(f"    Even error message loading failed: {final_error}")
                # If we can't even set plain text, the editor might be corrupted
                pass

        finally:
            # DEBUGGING: Log completion stats
            load_total_time = time.time() - load_start_time
            load_final_memory = process.memory_info().rss / 1024 / 1024  # MB
            load_memory_delta = load_final_memory - load_initial_memory

            logger.debug(f"    === LOAD DOCUMENT DEBUG COMPLETE ===")
            logger.debug(f"    Total load time: {load_total_time:.3f} seconds")
            logger.debug(f"    Load memory delta: {load_memory_delta:+.2f} MB")
            logger.debug(f"    Final load memory: {load_final_memory:.2f} MB")

    def _load_html_safely(self, editor, html_content) -> None:
        """Load HTML content safely to prevent UI blocking and freezing.

        Args:
            editor: RTF Editor window instance
            html_content: HTML content string to load
        """
        import time
        import psutil
        import os

        # DEBUGGING: Start HTML load timing
        html_start_time = time.time()
        process = psutil.Process(os.getpid())
        html_initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        logger.debug(f"      === HTML LOAD DEBUG START ===")
        logger.debug(f"      HTML content size: {len(html_content):,} characters ({len(html_content)/1024:.2f} KB)")
        logger.debug(f"      HTML load start memory: {html_initial_memory:.2f} MB")

        try:
            # Check if content contains images
            has_images = '<img ' in html_content.lower()

            # For modern systems with plenty of RAM, load HTML directly (no arbitrary limits)
            if len(html_content) < 100000000 or has_images:  # 100MB threshold for modern systems, always load images as HTML
                direct_load_start = time.time()
                if has_images:
                    logger.debug(f"      Loading HTML directly (contains images)")
                else:
                    logger.debug(f"      Loading HTML directly (small content)")
                editor.text_edit.setHtml(html_content)
                direct_load_time = time.time() - direct_load_start
                logger.debug(f"      Direct HTML load took: {direct_load_time:.3f} seconds")
                logger.debug(f"      Loaded HTML content directly ({len(html_content)} chars)")
                return

            # For larger content WITHOUT images, convert to plain text to avoid freezing
            logger.warning(f"      HTML content large ({len(html_content)} chars) and no images, converting to plain text")
            conversion_start = time.time()
            plain_text = self._html_to_plain_text(html_content)
            conversion_time = time.time() - conversion_start
            logger.debug(f"      HTML to plain text conversion took: {conversion_time:.3f} seconds")

            plain_load_start = time.time()
            editor.text_edit.setPlainText(plain_text)
            plain_load_time = time.time() - plain_load_start
            logger.debug(f"      Plain text load took: {plain_load_time:.3f} seconds")
            logger.debug(f"      Loaded large HTML as plain text ({len(plain_text)} chars)")

        except Exception as e:
            error_time = time.time()
            html_error_memory = process.memory_info().rss / 1024 / 1024  # MB

            logger.error(f"      HTML LOAD ERROR at {error_time}: Failed to load HTML safely: {e}")
            logger.error(f"      HTML error memory: {html_error_memory:.2f} MB")
            logger.error(f"      Error occurred after {error_time - html_start_time:.3f} seconds from HTML load start")

            # Fallback to plain text
            try:
                fallback_start = time.time()
                logger.debug(f"      Attempting fallback plain text conversion...")
                # Strip HTML tags for plain text fallback
                plain_text = self._html_to_plain_text(html_content)
                editor.text_edit.setPlainText(plain_text[:50000000])  # Limit to 50MB for modern systems
                fallback_time = time.time() - fallback_start
                logger.debug(f"      Fallback conversion took: {fallback_time:.3f} seconds")
                logger.debug("      Used fallback plain text conversion")
            except Exception as fallback_error:
                logger.error(f"      HTML and plain text fallback both failed: {fallback_error}")
                editor.text_edit.setPlainText(f"[Error loading document content: {str(e)}]")

        finally:
            # DEBUGGING: Log HTML load completion stats
            html_total_time = time.time() - html_start_time
            html_final_memory = process.memory_info().rss / 1024 / 1024  # MB
            html_memory_delta = html_final_memory - html_initial_memory

            logger.debug(f"      === HTML LOAD DEBUG COMPLETE ===")
            logger.debug(f"      Total HTML load time: {html_total_time:.3f} seconds")
            logger.debug(f"      HTML load memory delta: {html_memory_delta:+.2f} MB")
            logger.debug(f"      Final HTML load memory: {html_final_memory:.2f} MB")

    def _html_to_plain_text(self, html_content: str) -> str:
        """Convert HTML content to plain text safely.

        Args:
            html_content: HTML content to convert

        Returns:
            Plain text version of the content
        """
        try:
            import re

            # Remove script and style elements completely
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

            # Convert common HTML elements to text equivalents
            html_content = re.sub(r'<br\s*/?>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<p[^>]*>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</p>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<h[1-6][^>]*>', '\n\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</h[1-6]>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<li[^>]*>', '\n• ', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'</li>', '', html_content, flags=re.IGNORECASE)

            # Handle images specially - convert to placeholder text
            html_content = re.sub(
                r'<img[^>]*alt=["\']([^"\']*)["\'][^>]*>',
                r'\n[IMAGE: \1]\n',
                html_content,
                flags=re.IGNORECASE
            )
            # Handle images without alt text
            html_content = re.sub(
                r'<img[^>]*>',
                r'\n[IMAGE]\n',
                html_content,
                flags=re.IGNORECASE
            )

            # Remove all remaining HTML tags (except images which are already handled)
            html_content = re.sub(r'<[^>]+>', '', html_content)

            # Decode HTML entities
            html_content = html_content.replace('&amp;', '&')
            html_content = html_content.replace('&lt;', '<')
            html_content = html_content.replace('&gt;', '>')
            html_content = html_content.replace('&quot;', '"')
            html_content = html_content.replace('&#x27;', "'")
            html_content = html_content.replace('&nbsp;', ' ')

            # Clean up whitespace
            html_content = re.sub(r'\n\s*\n\s*\n', '\n\n', html_content)  # Remove excessive line breaks
            html_content = re.sub(r'[ \t]+', ' ', html_content)  # Normalize spaces
            html_content = html_content.strip()

            return html_content

        except Exception as e:
            logger.error(f"Error converting HTML to plain text: {e}")
            # Last resort - just remove tags
            import re
            return re.sub(r'<[^>]+>', '', html_content)

    def _is_valid_html_content(self, html_content: str) -> bool:
        """Validate HTML content to prevent loading malformed HTML that could cause freezing.

        Args:
            html_content: HTML content to validate

        Returns:
            True if content appears to be valid HTML, False otherwise
        """
        try:
            if not html_content or not isinstance(html_content, str):
                return False

            # Must contain some HTML-like content
            if '<' not in html_content or '>' not in html_content:
                return False

            # Check for potentially problematic patterns
            import re

            # Check for excessive nesting (could cause parser issues)
            nesting_depth = 0
            max_nesting = 0
            for char in html_content:
                if char == '<':
                    nesting_depth += 1
                    max_nesting = max(max_nesting, nesting_depth)
                elif char == '>':
                    nesting_depth = max(0, nesting_depth - 1)

            if max_nesting > 100:  # Arbitrary limit to prevent excessive nesting
                logger.warning(f"HTML content has excessive nesting depth: {max_nesting}")
                return False

            # Check for extremely long attributes or content that could cause issues
            if re.search(r'[a-zA-Z0-9_-]{10000,}', html_content):
                logger.warning("HTML content contains extremely long strings")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating HTML content: {e}")
            return False

    def _extract_plain_text_content(self, document_format) -> str:
        """Extract plain text content from document format using multiple fallback methods.

        Args:
            document_format: DocumentFormat object

        Returns:
            Plain text content or empty string if none found
        """
        try:
            # Try multiple attributes in order of preference
            content_attributes = [
                'plain_text',
                'content',
                'text',
                'body',
                'document_content',
                'raw_content'
            ]

            for attr in content_attributes:
                if hasattr(document_format, attr):
                    content = getattr(document_format, attr)
                    if content and isinstance(content, str) and content.strip():
                        logger.debug(f"Extracted content from attribute: {attr}")
                        return content.strip()

            # If no plain text found, try converting HTML content
            if hasattr(document_format, 'html_content') and document_format.html_content:
                logger.debug("Converting HTML content to plain text")
                return self._html_to_plain_text(document_format.html_content)

            return ""

        except Exception as e:
            logger.error(f"Error extracting plain text content: {e}")
            return ""

    def _truncate_html_safely(self, html_content: str, max_size: int) -> str:
        """Truncate HTML content safely without breaking HTML structure.

        Args:
            html_content: HTML content to truncate
            max_size: Maximum size in characters

        Returns:
            Safely truncated HTML content
        """
        try:
            if len(html_content) <= max_size:
                return html_content

            # Find a safe truncation point that doesn't break HTML tags
            truncated = html_content[:max_size]

            # Find the last complete tag before the truncation point
            last_tag_end = truncated.rfind('>')
            last_tag_start = truncated.rfind('<', 0, last_tag_end)

            if last_tag_start != -1 and last_tag_end != -1:
                # Truncate at the end of the last complete tag
                truncated = truncated[:last_tag_end + 1]
            else:
                # If no complete tags found, convert to plain text
                logger.warning("No complete HTML tags found, converting to plain text")
                return self._html_to_plain_text(html_content)[:max_size]

            # Add closing tags if needed to make valid HTML
            truncated = self._ensure_html_closure(truncated)

            return truncated

        except Exception as e:
            logger.error(f"Error truncating HTML safely: {e}")
            # Fallback to plain text truncation
            return self._html_to_plain_text(html_content)[:max_size]

    def _ensure_html_closure(self, html_content: str) -> str:
        """Ensure HTML content has proper closing tags.

        Args:
            html_content: HTML content to check

        Returns:
            HTML content with proper closing tags
        """
        try:
            # Simple approach: if content doesn't end with </body></html>,
            # convert to plain text to avoid broken HTML
            if not html_content.strip().endswith(('</html>', '</body>', '</p>', '</div>')):
                logger.debug("HTML content doesn't end properly, converting to plain text")
                return self._html_to_plain_text(html_content)

            return html_content

        except Exception as e:
            logger.error(f"Error ensuring HTML closure: {e}")
            return self._html_to_plain_text(html_content)
