"""
Document Conversion Dialog for RTF Editor Integration

This dialog provides a user-friendly interface for converting and importing
various document formats into the RTF editor system.
"""

import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from PyQt6.QtCore import QThread, QTimer, pyqtSignal, Qt
from PyQt6.QtGui import QIcon, QPixmap
from PyQt6.QtWidgets import (
    QCheckBox, QComboBox, QDialog, QDialogButtonBox, QFileDialog,
    QFrame, QGroupBox, QHBoxLayout, QLabel, QListWidget, QListWidgetItem,
    QProgressBar, QPushButton, QSplitter, QTextEdit, QVBoxLayout, QWidget,
    QMessageBox
)

from document_manager.services.document_conversion_service import DocumentConversionService
from shared.ui.widgets.rtf_editor.models.document_format import DocumentFormat

logger = logging.getLogger(__name__)


class ConversionWorker(QThread):
    """Worker thread for document conversion operations."""
    
    progress_updated = pyqtSignal(int, int, str)  # current, total, status
    conversion_completed = pyqtSignal(list)  # List of (file_path, DocumentFormat) tuples
    error_occurred = pyqtSignal(str)  # Error message
    
    def __init__(self, conversion_service: DocumentConversionService, 
                 file_paths: List[str], preserve_formatting: bool = True):
        super().__init__()
        self.conversion_service = conversion_service
        self.file_paths = file_paths
        self.preserve_formatting = preserve_formatting
        self._stop_requested = False
    
    def run(self):
        """Run the conversion process."""
        try:
            def progress_callback(current, total, status):
                if not self._stop_requested:
                    self.progress_updated.emit(current, total, status)
            
            results = self.conversion_service.batch_convert_files(
                self.file_paths,
                preserve_formatting=self.preserve_formatting,
                progress_callback=progress_callback
            )
            
            if not self._stop_requested:
                self.conversion_completed.emit(results)
                
        except Exception as e:
            if not self._stop_requested:
                self.error_occurred.emit(str(e))
    
    def stop(self):
        """Request stopping the conversion."""
        self._stop_requested = True


class DocumentConversionDialog(QDialog):
    """Dialog for converting documents to RTF editor format."""
    
    documents_converted = pyqtSignal(list)  # List of DocumentFormat objects
    
    def __init__(self, parent=None, conversion_service: Optional[DocumentConversionService] = None):
        super().__init__(parent)
        self.conversion_service = conversion_service or DocumentConversionService()
        self.conversion_worker = None
        self.converted_documents = []
        
        self.setWindowTitle("Import Documents to RTF Editor")
        self.setModal(True)
        self.resize(900, 700)
        
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Import Documents to RTF Editor")
        header_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(header_label)
        
        # Description
        desc_label = QLabel(
            "Select documents to convert and import into the RTF editor. "
            "Supported formats: PDF, DOCX, TXT, ODT, ODS, ODP"
        )
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin: 5px 10px 15px 10px;")
        layout.addWidget(desc_label)
        
        # Main content area
        main_splitter = QSplitter()
        layout.addWidget(main_splitter)
        
        # Left panel - File selection
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # File selection group
        file_group = QGroupBox("File Selection")
        file_layout = QVBoxLayout(file_group)
        
        # Buttons for file selection
        btn_layout = QHBoxLayout()
        
        self.select_files_btn = QPushButton("Select Files...")
        self.select_files_btn.clicked.connect(self.select_files)
        btn_layout.addWidget(self.select_files_btn)
        
        self.select_folder_btn = QPushButton("Select Folder...")
        self.select_folder_btn.clicked.connect(self.select_folder)
        btn_layout.addWidget(self.select_folder_btn)
        
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear_files)
        btn_layout.addWidget(self.clear_btn)
        
        file_layout.addLayout(btn_layout)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.setMinimumHeight(200)
        file_layout.addWidget(self.file_list)
        
        left_layout.addWidget(file_group)
        
        # Options group
        options_group = QGroupBox("Conversion Options")
        options_layout = QVBoxLayout(options_group)
        
        self.preserve_formatting_cb = QCheckBox("Preserve original formatting")
        self.preserve_formatting_cb.setChecked(True)
        self.preserve_formatting_cb.setToolTip(
            "Attempt to preserve formatting from source documents (headings, tables, etc.)"
        )
        options_layout.addWidget(self.preserve_formatting_cb)
        
        self.recursive_cb = QCheckBox("Include subdirectories")
        self.recursive_cb.setChecked(True)
        self.recursive_cb.setToolTip("Include files from subdirectories when selecting a folder")
        options_layout.addWidget(self.recursive_cb)
        
        left_layout.addWidget(options_group)
        
        # Statistics group
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("No files selected")
        self.stats_label.setStyleSheet("color: #666;")
        stats_layout.addWidget(self.stats_label)
        
        left_layout.addWidget(stats_group)
        
        left_layout.addStretch()
        main_splitter.addWidget(left_panel)
        
        # Right panel - Progress and results
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Progress group
        progress_group = QGroupBox("Conversion Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("Ready to convert...")
        self.status_label.setStyleSheet("color: #666;")
        progress_layout.addWidget(self.status_label)
        
        right_layout.addWidget(progress_group)
        
        # Results group
        results_group = QGroupBox("Conversion Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_list = QListWidget()
        self.results_list.setMinimumHeight(200)
        results_layout.addWidget(self.results_list)
        
        right_layout.addWidget(results_group)
        
        # Log group
        log_group = QGroupBox("Conversion Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        right_layout.addWidget(log_group)
        
        main_splitter.addWidget(right_panel)
        main_splitter.setSizes([400, 500])
        
        # Button box
        button_layout = QHBoxLayout()
        
        self.convert_btn = QPushButton("Convert Documents")
        self.convert_btn.setEnabled(False)
        self.convert_btn.setStyleSheet(
            "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }"
        )
        button_layout.addWidget(self.convert_btn)
        
        self.cancel_btn = QPushButton("Cancel Conversion")
        self.cancel_btn.setEnabled(False)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.import_btn = QPushButton("Import to Editor")
        self.import_btn.setEnabled(False)
        self.import_btn.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }"
        )
        button_layout.addWidget(self.import_btn)
        
        self.close_btn = QPushButton("Close")
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)

    def setup_connections(self):
        """Set up signal connections."""
        self.convert_btn.clicked.connect(self.start_conversion)
        self.cancel_btn.clicked.connect(self.cancel_conversion)
        self.import_btn.clicked.connect(self.import_documents)
        self.close_btn.clicked.connect(self.close)
        
        self.file_list.itemChanged.connect(self.update_statistics)

    def select_files(self):
        """Open file dialog to select individual files."""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        file_dialog.setWindowTitle("Select Documents to Convert")
        
        # Set file filters based on supported formats
        filters = []
        for ext, desc in self.conversion_service.get_supported_formats().items():
            filters.append(f"{desc} (*{ext})")
        filters.append("All Supported Files (*" + " *".join(self.conversion_service.get_supported_formats().keys()) + ")")
        filters.append("All Files (*)")
        
        file_dialog.setNameFilters(filters)
        
        if file_dialog.exec() == QFileDialog.DialogCode.Accepted:
            file_paths = file_dialog.selectedFiles()
            self.add_files(file_paths)

    def select_folder(self):
        """Open folder dialog to select a directory."""
        folder_dialog = QFileDialog(self)
        folder_dialog.setFileMode(QFileDialog.FileMode.Directory)
        folder_dialog.setWindowTitle("Select Folder to Convert")
        
        if folder_dialog.exec() == QFileDialog.DialogCode.Accepted:
            folder_path = folder_dialog.selectedFiles()[0]
            self.add_folder(folder_path)

    def add_files(self, file_paths: List[str]):
        """Add files to the conversion list."""
        added_count = 0
        
        for file_path in file_paths:
            file_path = Path(file_path)
            
            # Check if file already exists in list
            existing_items = [self.file_list.item(i).data(32) for i in range(self.file_list.count())]
            if str(file_path) in existing_items:
                continue
            
            # Check if format is supported
            if not self.conversion_service.is_format_supported(file_path):
                self.log_text.append(f"⚠️ Unsupported format: {file_path.name}")
                continue
            
            # Add to list
            item = QListWidgetItem()
            item.setText(f"{file_path.name} ({file_path.suffix.upper()})")
            item.setData(32, str(file_path))  # Store full path
            item.setCheckState(Qt.CheckState.Checked)  # Set as checked by default
            
            # Add format-specific icon or styling
            if file_path.suffix.lower() == '.pdf':
                item.setToolTip(f"PDF Document\n{file_path}")
            elif file_path.suffix.lower() == '.docx':
                item.setToolTip(f"Microsoft Word Document\n{file_path}")
            elif file_path.suffix.lower() == '.txt':
                item.setToolTip(f"Plain Text File\n{file_path}")
            else:
                item.setToolTip(f"Document File\n{file_path}")
            
            self.file_list.addItem(item)
            added_count += 1
        
        if added_count > 0:
            self.log_text.append(f"✅ Added {added_count} files to conversion list")
            self.update_statistics()
            self.convert_btn.setEnabled(True)

    def add_folder(self, folder_path: str):
        """Add all supported files from a folder."""
        folder_path = Path(folder_path)
        recursive = self.recursive_cb.isChecked()
        
        # Find all supported files
        file_paths = []
        pattern = "**/*" if recursive else "*"
        
        for file_path in folder_path.glob(pattern):
            if file_path.is_file() and self.conversion_service.is_format_supported(file_path):
                file_paths.append(str(file_path))
        
        if file_paths:
            self.add_files(file_paths)
            self.log_text.append(f"📁 Added {len(file_paths)} files from folder: {folder_path.name}")
        else:
            self.log_text.append(f"❌ No supported files found in: {folder_path.name}")

    def clear_files(self):
        """Clear all files from the list."""
        self.file_list.clear()
        self.results_list.clear()
        self.converted_documents.clear()
        self.convert_btn.setEnabled(False)
        self.import_btn.setEnabled(False)
        self.update_statistics()
        self.log_text.append("🗑️ Cleared file list")

    def update_statistics(self):
        """Update the statistics display."""
        total_files = self.file_list.count()
        checked_files = sum(1 for i in range(total_files) if self.file_list.item(i).checkState() == Qt.CheckState.Checked)
        
        if total_files == 0:
            self.stats_label.setText("No files selected")
        else:
            # Get format statistics
            file_paths = [self.file_list.item(i).data(32) for i in range(total_files) 
                         if self.file_list.item(i).checkState() == Qt.CheckState.Checked]
            
            if file_paths:
                stats = self.conversion_service.get_conversion_statistics(file_paths)
                format_text = ", ".join([f"{count} {fmt}" for fmt, count in stats['formats'].items()])
                self.stats_label.setText(
                    f"Selected: {checked_files}/{total_files} files\n"
                    f"Formats: {format_text}"
                )
            else:
                self.stats_label.setText(f"Selected: 0/{total_files} files")

    def start_conversion(self):
        """Start the document conversion process."""
        # Get selected files
        selected_files = []
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                selected_files.append(item.data(32))
        
        if not selected_files:
            QMessageBox.warning(self, "No Files Selected", "Please select at least one file to convert.")
            return
        
        # Update UI for conversion
        self.convert_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(selected_files))
        self.progress_bar.setValue(0)
        self.results_list.clear()
        
        self.status_label.setText("Starting conversion...")
        self.log_text.append(f"🔄 Starting conversion of {len(selected_files)} files...")
        
        # Start conversion worker
        preserve_formatting = self.preserve_formatting_cb.isChecked()
        self.conversion_worker = ConversionWorker(
            self.conversion_service, 
            selected_files, 
            preserve_formatting
        )
        
        self.conversion_worker.progress_updated.connect(self.on_progress_updated)
        self.conversion_worker.conversion_completed.connect(self.on_conversion_completed)
        self.conversion_worker.error_occurred.connect(self.on_conversion_error)
        
        self.conversion_worker.start()

    def cancel_conversion(self):
        """Cancel the ongoing conversion."""
        if self.conversion_worker and self.conversion_worker.isRunning():
            self.conversion_worker.stop()
            self.conversion_worker.wait(3000)  # Wait up to 3 seconds
            
        self.reset_ui()
        self.status_label.setText("Conversion cancelled")
        self.log_text.append("❌ Conversion cancelled by user")

    def on_progress_updated(self, current: int, total: int, status: str):
        """Handle progress updates."""
        self.progress_bar.setValue(current)
        self.status_label.setText(f"Converting {current}/{total}: {status}")

    def on_conversion_completed(self, results: List[Tuple[str, Optional[DocumentFormat]]]):
        """Handle conversion completion."""
        self.reset_ui()
        
        successful_conversions = []
        failed_conversions = []
        
        for file_path, doc_format in results:
            file_name = Path(file_path).name
            
            if doc_format:
                successful_conversions.append(doc_format)
                
                # Add to results list
                item = QListWidgetItem(f"✅ {file_name}")
                item.setData(32, doc_format)  # Store DocumentFormat
                item.setToolTip(f"Successfully converted: {file_path}")
                self.results_list.addItem(item)
            else:
                failed_conversions.append(file_path)
                
                # Add to results list
                item = QListWidgetItem(f"❌ {file_name}")
                item.setToolTip(f"Failed to convert: {file_path}")
                self.results_list.addItem(item)
        
        # Store successful conversions
        self.converted_documents = successful_conversions
        
        # Update UI
        if successful_conversions:
            self.import_btn.setEnabled(True)
        
        # Update status and log
        total_files = len(results)
        success_count = len(successful_conversions)
        fail_count = len(failed_conversions)
        
        self.status_label.setText(f"Conversion complete: {success_count}/{total_files} successful")
        self.log_text.append(f"✅ Conversion completed!")
        self.log_text.append(f"📊 Results: {success_count} successful, {fail_count} failed")
        
        if fail_count > 0:
            self.log_text.append("❌ Failed files:")
            for file_path in failed_conversions:
                self.log_text.append(f"   - {Path(file_path).name}")

    def on_conversion_error(self, error_message: str):
        """Handle conversion errors."""
        self.reset_ui()
        self.status_label.setText("Conversion failed")
        self.log_text.append(f"❌ Conversion error: {error_message}")
        
        QMessageBox.critical(
            self, 
            "Conversion Error", 
            f"An error occurred during conversion:\n\n{error_message}"
        )

    def reset_ui(self):
        """Reset UI to ready state."""
        self.convert_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress_bar.setVisible(False)

    def import_documents(self):
        """Import converted documents to RTF editor."""
        if not self.converted_documents:
            QMessageBox.warning(self, "No Documents", "No successfully converted documents to import.")
            return

        # Emit signal with converted documents
        self.documents_converted.emit(self.converted_documents)

        # Add a small delay to ensure RTF Editor windows are fully created before showing success dialog
        from PyQt6.QtCore import QTimer

        def show_success_dialog():
            # Show success message with proper z-ordering
            # Create message box with WindowStaysOnTopHint to ensure it appears above RTF Editor windows
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("Import Successful")
            msg_box.setText(f"Successfully imported {len(self.converted_documents)} documents to RTF editor.")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

            # Ensure the dialog stays on top of RTF Editor windows
            msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)

            # Show the dialog
            msg_box.exec()

            # Close dialog after success message
            self.accept()

        # Show success dialog after a brief delay to ensure RTF windows are created
        QTimer.singleShot(100, show_success_dialog)

    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.conversion_worker and self.conversion_worker.isRunning():
            reply = QMessageBox.question(
                self,
                "Conversion in Progress",
                "A conversion is currently running. Do you want to cancel it and close?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.cancel_conversion()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


# Example usage for testing
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = DocumentConversionDialog()
    dialog.show()
    
    sys.exit(app.exec())
