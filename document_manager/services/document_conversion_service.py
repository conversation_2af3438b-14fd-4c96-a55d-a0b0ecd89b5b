"""
Document Conversion Service for RTF Editor Integration

This service provides conversion capabilities between various document formats 
and the RTF editor's DocumentFormat system, leveraging the existing DocumentService
infrastructure for text extraction and format conversion.
"""

import logging
import tempfile
import base64
import io
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import uuid

from document_manager.models.document import Document
from document_manager.services.document_service import DocumentService
from document_manager.services.qgem_document_service import QGemDocumentService
from document_manager.services.image_aware_document_service import ImageAwareDocumentService
from shared.ui.widgets.rtf_editor.models.document_format import DocumentFormat

logger = logging.getLogger(__name__)


class DocumentConversionService:
    """Service for converting documents to RTF editor format.
    
    This service acts as a bridge between the existing document management
    infrastructure and the RTF editor, providing seamless conversion
    capabilities for various document formats.
    """

    def __init__(self):
        """Initialize the conversion service."""
        self.document_service = DocumentService()
        self.qgem_service = QGemDocumentService(self.document_service)
        self.image_service = ImageAwareDocumentService()
        
        # Supported input formats
        self.supported_formats = {
            '.pdf': 'PDF Document',
            '.docx': 'Microsoft Word Document',
            '.txt': 'Plain Text File',
            '.odt': 'OpenDocument Text',
            '.ods': 'OpenDocument Spreadsheet',
            '.odp': 'OpenDocument Presentation',
        }

    def get_supported_formats(self) -> Dict[str, str]:
        """Get dictionary of supported file formats.
        
        Returns:
            Dictionary mapping file extensions to format descriptions
        """
        return self.supported_formats.copy()

    def is_format_supported(self, file_path: Union[str, Path]) -> bool:
        """Check if a file format is supported for conversion.
        
        Args:
            file_path: Path to the file to check
            
        Returns:
            True if format is supported, False otherwise
        """
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_formats

    def convert_file_to_document_format(
        self,
        file_path: Union[str, Path],
        document_name: Optional[str] = None,
        preserve_formatting: bool = True
    ) -> Optional[DocumentFormat]:
        """Convert a file to DocumentFormat for RTF editor.

        Args:
            file_path: Path to the file to convert
            document_name: Optional custom name for the document
            preserve_formatting: Whether to preserve original formatting

        Returns:
            DocumentFormat object if successful, None otherwise
        """
        try:
            file_path = Path(file_path)

            # Validate file exists and format is supported
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return None

            if not self.is_format_supported(file_path):
                logger.error(f"Unsupported format: {file_path.suffix}")
                return None

            # Check file size before processing to prevent memory issues
            file_size = file_path.stat().st_size
            max_file_size = 50 * 1024 * 1024  # 50MB limit

            if file_size > max_file_size:
                logger.warning(f"File too large for conversion: {file_size} bytes (max: {max_file_size})")
                return None

            # Check if this is a format that supports image extraction
            file_extension = file_path.suffix.lower()
            logger.debug(f"File extension: {file_extension}, preserve_formatting: {preserve_formatting}")

            if file_extension in ['.pdf', '.docx'] and preserve_formatting:
                logger.info(f"Converting document with image extraction: {file_path} ({file_size} bytes)")
                logger.debug("Using image-aware conversion service")
                return self._convert_file_with_images(file_path, document_name or file_path.stem)
            else:
                # Use existing text-only conversion for other formats
                logger.info(f"Converting document (text-only): {file_path} ({file_size} bytes)")
                logger.debug(f"Using text-only conversion: extension={file_extension}, preserve_formatting={preserve_formatting}")
                document = self.document_service.import_document(str(file_path))

                if not document:
                    logger.error(f"Failed to import document: {file_path}")
                    return None

                # Check content size after import (very generous limit for modern systems)
                if document.content and len(document.content) > 100000000:  # 100MB of text
                    logger.warning(f"Document content extremely large: {len(document.content)} characters")
                    # Only truncate truly massive documents
                    document.content = document.content[:100000000] + "\n\n[Content truncated due to extreme size]"

                # Convert to DocumentFormat
                return self._convert_document_to_format(
                    document,
                    document_name or file_path.stem,
                    preserve_formatting
                )

        except Exception as e:
            logger.error(f"Error converting file {file_path}: {str(e)}", exc_info=True)
            return None

    def convert_document_to_format(
        self, 
        document: Document,
        preserve_formatting: bool = True
    ) -> Optional[DocumentFormat]:
        """Convert an existing Document to DocumentFormat.
        
        Args:
            document: Document instance to convert
            preserve_formatting: Whether to preserve original formatting
            
        Returns:
            DocumentFormat object if successful, None otherwise
        """
        try:
            return self._convert_document_to_format(
                document, 
                document.name,
                preserve_formatting
            )
        except Exception as e:
            logger.error(f"Error converting document {document.id}: {str(e)}", exc_info=True)
            return None

    def _convert_file_with_images(self, file_path: Path, document_name: str) -> Optional[DocumentFormat]:
        """Convert a file to DocumentFormat with image extraction and positioning.

        Args:
            file_path: Path to the file to convert
            document_name: Name for the document

        Returns:
            DocumentFormat object with embedded images if successful, None otherwise
        """
        try:
            # Extract text content and images using the image-aware service
            logger.debug(f"Extracting content with images from: {file_path}")
            text_content, images = self.image_service.extract_content_with_images(file_path)

            logger.debug(f"Extracted {len(text_content)} chars of text and {len(images)} images")

            if not text_content and not images:
                logger.warning(f"No content extracted from {file_path}")
                return None

            # Create HTML content with embedded images
            logger.debug("Creating HTML with embedded images")
            html_content = self.image_service.create_html_with_embedded_images(text_content, images)
            logger.debug(f"Generated HTML content: {len(html_content)} chars")

            # Validate and limit HTML content size (very generous for modern systems)
            if html_content and len(html_content) > 100000000:  # 100MB HTML limit
                logger.warning(f"HTML content with images extremely large ({len(html_content)} chars), truncating")
                html_content = html_content[:100000000] + "\n\n[HTML content truncated due to extreme size]"

            # Calculate statistics
            word_count = len(text_content.split()) if text_content else 0
            character_count = len(text_content) if text_content else 0

            # Create DocumentFormat with image metadata
            doc_format = DocumentFormat(
                id=str(uuid.uuid4()),
                name=document_name,
                html_content=html_content,
                plain_text=text_content,
                created_at=datetime.now(),
                modified_at=datetime.now(),
                word_count=word_count,
                character_count=character_count,
                metadata={
                    'original_format': file_path.suffix,
                    'original_path': str(file_path),
                    'has_images': len(images) > 0,
                    'image_count': len(images),
                    'image_extraction_enabled': True,
                    'converted_at': datetime.now().isoformat(),
                    'conversion_service_version': '2.0',
                    'images': [
                        {
                            'id': img.id,
                            'width': img.width,
                            'height': img.height,
                            'position': img.position,
                            'alt_text': img.alt_text,
                            'caption': img.caption
                        } for img in images
                    ]
                }
            )

            logger.info(f"Successfully converted document '{document_name}' with {len(images)} images")
            return doc_format

        except Exception as e:
            logger.error(f"Error converting file with images {file_path}: {str(e)}", exc_info=True)
            return None

    def _convert_document_to_format(
        self,
        document: Document,
        document_name: str,
        preserve_formatting: bool = True
    ) -> DocumentFormat:
        """Internal method to convert Document to DocumentFormat.

        Args:
            document: Document instance to convert
            document_name: Name for the document
            preserve_formatting: Whether to preserve original formatting

        Returns:
            DocumentFormat object
        """
        # Extract content with formatting if requested
        html_content = ""

        try:
            if preserve_formatting and hasattr(document, 'metadata') and document.metadata:
                # Check if we have formatted text
                has_formatted_text = document.metadata.get('formatted_text', False)

                if has_formatted_text:
                    html_content = self._convert_to_html(document)
                else:
                    html_content = self._plain_text_to_html(document.content)
            else:
                html_content = self._plain_text_to_html(document.content)

            # Validate and limit HTML content size (very generous for modern systems)
            if html_content and len(html_content) > 100000000:  # 100MB HTML limit
                logger.warning(f"HTML content extremely large ({len(html_content)} chars), truncating")
                html_content = html_content[:100000000] + "\n\n[HTML content truncated due to extreme size]"

        except Exception as e:
            logger.error(f"Error converting document content to HTML: {e}")
            # Fallback to plain text
            html_content = self._plain_text_to_html(document.content or "")

        # Create DocumentFormat
        doc_format = DocumentFormat(
            id=str(uuid.uuid4()),
            name=document_name,
            html_content=html_content,
            plain_text=document.content or "",
            created_at=document.creation_date if document.creation_date else datetime.now(),
            modified_at=document.last_modified_date if document.last_modified_date else datetime.now(),
            word_count=document.word_count or 0,
            character_count=len(document.content) if document.content else 0,
            metadata={
                'original_format': Path(document.file_path).suffix if document.file_path else 'unknown',
                'original_path': str(document.file_path) if document.file_path else None,
                'has_greek_text': document.metadata.get('has_greek_text', False) if document.metadata else False,
                'extraction_status': document.metadata.get('extraction_status', 'unknown') if document.metadata else 'unknown',
                'source_document_id': document.id,
                'converted_at': datetime.now().isoformat(),
                'conversion_service_version': '1.0'
            }
        )
        
        logger.info(f"Successfully converted document '{document_name}' to DocumentFormat")
        return doc_format

    def _convert_to_html(self, document: Document) -> str:
        """Convert document content to HTML format.
        
        Args:
            document: Document to convert
            
        Returns:
            HTML formatted content
        """
        content = document.content or ""
        
        # Handle different document types
        file_extension = Path(document.file_path).suffix.lower() if document.file_path else ""
        
        if file_extension == '.docx':
            return self._convert_docx_to_html(content)
        elif file_extension in ['.odt', '.ods', '.odp']:
            return self._convert_odf_to_html(content)
        else:
            return self._plain_text_to_html(content)

    def _convert_docx_to_html(self, content: str) -> str:
        """Convert DOCX extracted content to HTML.
        
        Args:
            content: Extracted text content from DOCX
            
        Returns:
            HTML formatted content
        """
        html_parts = ['<html><body>']
        
        # Process content line by line to preserve formatting indicators
        lines = content.split('\n')
        in_table = False
        
        for line in lines:
            line = line.strip()

            if not line:
                html_parts.append('<br>')
                continue
                
            # Handle table markers
            if line == '[TABLE]':
                html_parts.append('<table border="1" style="border-collapse: collapse; width: 100%;">')
                in_table = True
                continue
            elif line == '[/TABLE]':
                html_parts.append('</table>')
                in_table = False
                continue
            elif in_table and '|' in line:
                # Table row
                cells = [cell.strip() for cell in line.split('|')]
                html_parts.append('<tr>')
                for cell in cells:
                    html_parts.append(f'<td style="padding: 4px; border: 1px solid #ccc;">{self._escape_html(cell)}</td>')
                html_parts.append('</tr>')
                continue
            elif in_table and line.startswith('---'):
                # Table separator - skip
                continue
                
            # Handle headings
            if line.startswith('######'):
                html_parts.append(f'<h6>{self._escape_html(line[6:].strip())}</h6>')
            elif line.startswith('#####'):
                html_parts.append(f'<h5>{self._escape_html(line[5:].strip())}</h5>')
            elif line.startswith('####'):
                html_parts.append(f'<h4>{self._escape_html(line[4:].strip())}</h4>')
            elif line.startswith('###'):
                html_parts.append(f'<h3>{self._escape_html(line[3:].strip())}</h3>')
            elif line.startswith('##'):
                html_parts.append(f'<h2>{self._escape_html(line[2:].strip())}</h2>')
            elif line.startswith('#'):
                html_parts.append(f'<h1>{self._escape_html(line[1:].strip())}</h1>')
            else:
                # Regular paragraph
                html_parts.append(f'<p>{self._escape_html(line)}</p>')
        
        html_parts.append('</body></html>')
        return '\n'.join(html_parts)

    def _convert_odf_to_html(self, content: str) -> str:
        """Convert ODF extracted content to HTML.
        
        Args:
            content: Extracted text content from ODF
            
        Returns:
            HTML formatted content
        """
        # ODF content is typically well-structured
        return self._plain_text_to_html(content)

    def _plain_text_to_html(self, content: str) -> str:
        """Convert plain text to basic HTML safely.

        Args:
            content: Plain text content

        Returns:
            HTML formatted content
        """
        if not content:
            return '<html><body><p></p></body></html>'

        try:
            # Limit content size only for truly massive documents
            if len(content) > 100000000:  # 100MB limit for plain text (very generous)
                logger.warning(f"Plain text content extremely large ({len(content)} chars), truncating")
                content = content[:100000000] + "\n\n[Content truncated due to extreme size]"

            html_parts = ['<html><body>']

            # Split into paragraphs and convert
            paragraphs = content.split('\n\n')

            # Limit number of paragraphs only for truly excessive documents
            if len(paragraphs) > 100000:
                logger.warning(f"Extremely large number of paragraphs ({len(paragraphs)}), limiting to 100000")
                paragraphs = paragraphs[:100000]
                paragraphs.append("[Content truncated - extremely large document]")

            for para in paragraphs:
                para = para.strip()
                if para:
                    # Escape HTML first to prevent issues with user content
                    escaped_para = self._escape_html(para)
                    # Then replace line breaks with <br> tags (after escaping)
                    escaped_para = escaped_para.replace('\n', '<br>')
                    # Limit paragraph length only for truly massive paragraphs
                    if len(escaped_para) > 1000000:  # Limit individual paragraph size to 1MB
                        escaped_para = escaped_para[:1000000] + "...[paragraph truncated]"
                    html_parts.append(f'<p>{escaped_para}</p>')
                else:
                    html_parts.append('<br>')

            html_parts.append('</body></html>')
            result = '\n'.join(html_parts)

            # Final size check (very generous for modern systems)
            if len(result) > 100000000:  # 100MB final HTML limit
                logger.warning("Generated HTML extremely large, falling back to simple conversion")
                # Simple fallback
                escaped_content = self._escape_html(content[:50000000])  # 50MB limit
                result = f'<html><body><pre>{escaped_content}</pre></body></html>'

            return result

        except Exception as e:
            logger.error(f"Error converting plain text to HTML: {e}")
            # Last resort fallback
            try:
                safe_content = self._escape_html(content[:10000000] if content else "")  # 10MB fallback limit
                return f'<html><body><pre>{safe_content}</pre></body></html>'
            except Exception as final_error:
                logger.error(f"Even fallback HTML conversion failed: {final_error}")
                return '<html><body><p>[Error converting content to HTML]</p></body></html>'

    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters.
        
        Args:
            text: Text to escape
            
        Returns:
            HTML-escaped text
        """
        if not text:
            return ""
            
        replacements = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;'
        }
        
        for char, escape in replacements.items():
            text = text.replace(char, escape)
            
        return text

    def batch_convert_files(
        self, 
        file_paths: List[Union[str, Path]],
        preserve_formatting: bool = True,
        progress_callback: Optional[callable] = None
    ) -> List[Tuple[str, Optional[DocumentFormat]]]:
        """Convert multiple files to DocumentFormat.
        
        Args:
            file_paths: List of file paths to convert
            preserve_formatting: Whether to preserve original formatting
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of tuples (file_path, DocumentFormat or None)
        """
        results = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            try:
                if progress_callback:
                    progress_callback(i + 1, total_files, f"Converting {Path(file_path).name}")
                
                doc_format = self.convert_file_to_document_format(
                    file_path, 
                    preserve_formatting=preserve_formatting
                )
                results.append((str(file_path), doc_format))
                
            except Exception as e:
                logger.error(f"Error converting {file_path}: {str(e)}", exc_info=True)
                results.append((str(file_path), None))
        
        return results

    def convert_directory(
        self,
        directory_path: Union[str, Path],
        recursive: bool = True,
        preserve_formatting: bool = True,
        progress_callback: Optional[callable] = None
    ) -> List[Tuple[str, Optional[DocumentFormat]]]:
        """Convert all supported files in a directory.
        
        Args:
            directory_path: Path to directory to convert
            recursive: Whether to search subdirectories
            preserve_formatting: Whether to preserve original formatting
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of tuples (file_path, DocumentFormat or None)
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists() or not directory_path.is_dir():
            logger.error(f"Directory not found: {directory_path}")
            return []
        
        # Find all supported files
        file_paths = []
        pattern = "**/*" if recursive else "*"
        
        for file_path in directory_path.glob(pattern):
            if file_path.is_file() and self.is_format_supported(file_path):
                file_paths.append(file_path)
        
        logger.info(f"Found {len(file_paths)} supported files in {directory_path}")
        
        return self.batch_convert_files(
            file_paths, 
            preserve_formatting=preserve_formatting,
            progress_callback=progress_callback
        )

    def create_import_dialog_integration(self, parent=None):
        """Create a dialog for importing documents into RTF editor.
        
        Args:
            parent: Parent widget for the dialog
            
        Returns:
            Dialog instance for document import
        """
        try:
            from document_manager.ui.dialogs.document_conversion_dialog import DocumentConversionDialog
            return DocumentConversionDialog(parent, self)
        except ImportError:
            logger.warning("DocumentConversionDialog not available")
            return None

    def get_conversion_statistics(self, file_paths: List[Union[str, Path]]) -> Dict[str, int]:
        """Get statistics about convertible files.
        
        Args:
            file_paths: List of file paths to analyze
            
        Returns:
            Dictionary with conversion statistics
        """
        stats = {
            'total_files': len(file_paths),
            'supported_files': 0,
            'unsupported_files': 0,
            'formats': {}
        }
        
        for file_path in file_paths:
            file_path = Path(file_path)
            extension = file_path.suffix.lower()
            
            if self.is_format_supported(file_path):
                stats['supported_files'] += 1
                format_name = self.supported_formats.get(extension, extension)
                stats['formats'][format_name] = stats['formats'].get(format_name, 0) + 1
            else:
                stats['unsupported_files'] += 1
        
        return stats
