"""
Converted Document Service

This service handles saving converted DocumentFormat objects to the database
and provides advanced search functionality for converted documents.
"""

import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

from loguru import logger

from document_manager.models.document import Document, DocumentType
from document_manager.services.document_service import DocumentService
from shared.ui.widgets.rtf_editor.models.document_format import DocumentFormat


class ConvertedDocumentService:
    """Service for managing converted documents in the database."""
    
    def __init__(self):
        """Initialize the converted document service."""
        self.document_service = DocumentService()
    
    def save_converted_document(
        self, 
        doc_format: DocumentFormat, 
        original_file_path: Optional[Union[str, Path]] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        notes: Optional[str] = None
    ) -> Optional[Document]:
        """Save a converted DocumentFormat to the database as a Document.
        
        Args:
            doc_format: The DocumentFormat to save
            original_file_path: Path to the original file (if available)
            category: Document category
            tags: List of tags to apply
            notes: Additional notes
            
        Returns:
            The saved Document object, or None if saving failed
        """
        try:
            # Create a Document from the DocumentFormat
            document = self._document_format_to_document(
                doc_format, 
                original_file_path, 
                category, 
                tags, 
                notes
            )
            
            # Save the document
            saved_document = self.document_service.save_document(document)
            
            if saved_document:
                logger.info(f"Successfully saved converted document: {doc_format.name}")
                return saved_document
            else:
                logger.error(f"Failed to save converted document: {doc_format.name}")
                return None
                
        except Exception as e:
            logger.error(f"Error saving converted document {doc_format.name}: {e}")
            return None
    
    def _document_format_to_document(
        self,
        doc_format: DocumentFormat,
        original_file_path: Optional[Union[str, Path]] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        notes: Optional[str] = None
    ) -> Document:
        """Convert a DocumentFormat to a Document.
        
        Args:
            doc_format: The DocumentFormat to convert
            original_file_path: Path to the original file
            category: Document category
            tags: List of tags
            notes: Additional notes
            
        Returns:
            Document object
        """
        # Determine file path and type
        if original_file_path:
            file_path = Path(original_file_path)
            file_type = DocumentType.from_extension(file_path.suffix[1:] if file_path.suffix else "")
        else:
            # Create a virtual path for converted documents
            file_path = Path(f"converted/{doc_format.id}.qtdoc")
            file_type = DocumentType.QTDOC
        
        # Calculate size based on content
        content_size = len(doc_format.html_content) + len(doc_format.plain_text)
        
        # Prepare metadata
        metadata = {
            'converted_document': True,
            'conversion_id': doc_format.id,
            'has_images': len(doc_format.images) > 0,
            'image_count': len(doc_format.images),
            'has_tables': len(doc_format.tables) > 0,
            'table_count': len(doc_format.tables),
            'has_annotations': len(doc_format.annotations) > 0,
            'annotation_count': len(doc_format.annotations),
            'html_content_length': len(doc_format.html_content),
            'conversion_metadata': doc_format.metadata
        }
        
        # Create the Document
        document = Document(
            id=str(uuid.uuid4()),
            name=doc_format.name,
            file_path=file_path,
            file_type=file_type,
            size_bytes=content_size,
            creation_date=doc_format.created_at,
            last_modified_date=doc_format.modified_at,
            content=doc_format.html_content,  # Store HTML as primary content
            extracted_text=doc_format.plain_text,  # Store plain text for searching
            tags=set(tags) if tags else set(),
            category=category,
            notes=notes,
            word_count=doc_format.word_count,
            metadata=metadata
        )
        
        return document
    
    def search_converted_documents(
        self,
        query: Optional[str] = None,
        search_in_content: bool = True,
        case_sensitive: bool = False,
        whole_words_only: bool = False,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        has_images: Optional[bool] = None,
        has_tables: Optional[bool] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Tuple[Document, List[Dict]]]:
        """Search for converted documents with advanced options.
        
        Args:
            query: Search text
            search_in_content: Whether to search in document content
            case_sensitive: Whether search should be case sensitive
            whole_words_only: Whether to match whole words only
            category: Filter by category
            tags: Filter by tags
            has_images: Filter by presence of images
            has_tables: Filter by presence of tables
            date_from: Filter by creation date (from)
            date_to: Filter by creation date (to)
            limit: Maximum number of results
            
        Returns:
            List of tuples (Document, list of match positions)
        """
        try:
            # Get all converted documents first
            all_documents, _ = self.document_service.search_documents(
                category=category,
                tags=tags,
                date_from=date_from,
                date_to=date_to,
                limit=1000  # Get more for filtering
            )
            
            # Filter for converted documents only
            converted_docs = [
                doc for doc in all_documents 
                if doc.metadata and doc.metadata.get('converted_document', False)
            ]
            
            # Apply additional filters
            if has_images is not None:
                converted_docs = [
                    doc for doc in converted_docs
                    if doc.metadata.get('has_images', False) == has_images
                ]
            
            if has_tables is not None:
                converted_docs = [
                    doc for doc in converted_docs
                    if doc.metadata.get('has_tables', False) == has_tables
                ]
            
            # If no query, return filtered documents
            if not query:
                return [(doc, []) for doc in converted_docs[:limit]]
            
            # Perform text search with position tracking
            results = []
            for document in converted_docs:
                matches = self._find_text_matches(
                    document, 
                    query, 
                    search_in_content, 
                    case_sensitive, 
                    whole_words_only
                )
                
                if matches:
                    results.append((document, matches))
                
                if len(results) >= limit:
                    break
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching converted documents: {e}")
            return []
    
    def _find_text_matches(
        self,
        document: Document,
        query: str,
        search_in_content: bool,
        case_sensitive: bool,
        whole_words_only: bool
    ) -> List[Dict]:
        """Find text matches in a document with position information.
        
        Args:
            document: Document to search
            query: Search query
            search_in_content: Whether to search in content
            case_sensitive: Whether search is case sensitive
            whole_words_only: Whether to match whole words only
            
        Returns:
            List of match dictionaries with position information
        """
        import re
        
        matches = []
        
        # Prepare search text
        search_text = query if case_sensitive else query.lower()
        
        # Search in document name
        doc_name = document.name if case_sensitive else document.name.lower()
        if search_text in doc_name:
            matches.append({
                'field': 'name',
                'text': document.name,
                'position': doc_name.find(search_text),
                'length': len(query),
                'context': document.name
            })
        
        # Search in content if requested
        if search_in_content and document.extracted_text:
            content = document.extracted_text if case_sensitive else document.extracted_text.lower()
            
            if whole_words_only:
                # Use regex for whole word matching
                pattern = r'\b' + re.escape(search_text) + r'\b'
                flags = 0 if case_sensitive else re.IGNORECASE
                
                for match in re.finditer(pattern, content, flags):
                    # Get context around the match
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context = content[start:end]
                    
                    matches.append({
                        'field': 'content',
                        'text': match.group(),
                        'position': match.start(),
                        'length': len(match.group()),
                        'context': context,
                        'context_start': start
                    })
            else:
                # Simple substring search
                start = 0
                while True:
                    pos = content.find(search_text, start)
                    if pos == -1:
                        break
                    
                    # Get context around the match
                    context_start = max(0, pos - 50)
                    context_end = min(len(content), pos + len(search_text) + 50)
                    context = content[context_start:context_end]
                    
                    matches.append({
                        'field': 'content',
                        'text': search_text,
                        'position': pos,
                        'length': len(search_text),
                        'context': context,
                        'context_start': context_start
                    })
                    
                    start = pos + 1
        
        return matches
    
    def get_document_statistics(self) -> Dict:
        """Get statistics about converted documents.
        
        Returns:
            Dictionary with statistics
        """
        try:
            # Get all converted documents
            all_documents, total_count = self.document_service.search_documents(limit=10000)
            converted_docs = [
                doc for doc in all_documents 
                if doc.metadata and doc.metadata.get('converted_document', False)
            ]
            
            # Calculate statistics
            stats = {
                'total_converted_documents': len(converted_docs),
                'total_all_documents': total_count,
                'documents_with_images': sum(1 for doc in converted_docs if doc.metadata.get('has_images', False)),
                'documents_with_tables': sum(1 for doc in converted_docs if doc.metadata.get('has_tables', False)),
                'total_word_count': sum(doc.word_count or 0 for doc in converted_docs),
                'average_word_count': 0,
                'file_types': {},
                'categories': {}
            }
            
            if converted_docs:
                stats['average_word_count'] = stats['total_word_count'] / len(converted_docs)
                
                # Count file types
                for doc in converted_docs:
                    file_type = doc.file_type.value
                    stats['file_types'][file_type] = stats['file_types'].get(file_type, 0) + 1
                
                # Count categories
                for doc in converted_docs:
                    category = doc.category or 'Uncategorized'
                    stats['categories'][category] = stats['categories'].get(category, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting document statistics: {e}")
            return {}
