"""
Image-Aware Document Service

This service extends the basic document extraction capabilities to include
image extraction and positioning preservation for PDF and DOCX files.
"""

import base64
import io
import tempfile
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

from loguru import logger

try:
    import fitz  # PyMuPDF for PDF processing
except ImportError:
    fitz = None
    logger.warning("PyMuPDF not available - PDF image extraction disabled")

try:
    from docx import Document as DocxDocument
    from docx.shared import Inches
except ImportError:
    DocxDocument = None
    logger.warning("python-docx not available - DOCX image extraction disabled")

try:
    from PIL import Image
except ImportError:
    Image = None
    logger.warning("Pillow not available - image processing disabled")


class ImageInfo:
    """Information about an extracted image."""
    
    def __init__(self, data_uri: str, width: int, height: int, position: int, 
                 alt_text: str = "", caption: str = ""):
        self.data_uri = data_uri
        self.width = width
        self.height = height
        self.position = position  # Character position in text where image should be inserted
        self.alt_text = alt_text
        self.caption = caption
        self.id = str(uuid.uuid4())


class ImageAwareDocumentService:
    """Service for extracting text and images from documents with positioning."""
    
    def __init__(self):
        """Initialize the image-aware document service."""
        self.temp_dir = Path(tempfile.gettempdir()) / "isopgem_image_extraction"
        self.temp_dir.mkdir(exist_ok=True)
    
    def extract_content_with_images(self, file_path: Union[str, Path]) -> Tuple[str, List[ImageInfo]]:
        """Extract text content and images from a document.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Tuple of (text_content, list_of_image_info)
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        if extension == '.pdf':
            return self._extract_pdf_content_with_images(file_path)
        elif extension == '.docx':
            return self._extract_docx_content_with_images(file_path)
        else:
            # For other formats, just return text without images
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content, []
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {e}")
                return "", []
    
    def _extract_pdf_content_with_images(self, file_path: Path) -> Tuple[str, List[ImageInfo]]:
        """Extract text and images from PDF with positioning."""
        if not fitz:
            logger.error("PyMuPDF not available for PDF processing")
            return "", []
        
        try:
            doc = fitz.open(str(file_path))
            text_content = ""
            images = []
            current_position = 0
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Get text blocks with positioning information
                text_dict = page.get_text("dict")
                
                # Get images on this page
                image_list = page.get_images()
                
                # Process text blocks and insert image markers
                page_text = ""
                image_positions = []
                
                # Extract text blocks
                for block in text_dict["blocks"]:
                    if "lines" in block:  # Text block
                        for line in block["lines"]:
                            for span in line["spans"]:
                                page_text += span["text"]
                        page_text += "\n"
                
                # Extract images and determine their positions
                for img_index, img in enumerate(image_list):
                    try:
                        # Get image data
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)
                        
                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            img_data = pix.tobytes("png")
                            
                            # Convert to data URI
                            data_uri = f"data:image/png;base64,{base64.b64encode(img_data).decode()}"
                            
                            # Create image info
                            # For PDF, we'll insert images at the end of each page's text
                            image_position = current_position + len(page_text)
                            
                            image_info = ImageInfo(
                                data_uri=data_uri,
                                width=pix.width,
                                height=pix.height,
                                position=image_position,
                                alt_text=f"Image {img_index + 1} from page {page_num + 1}"
                            )
                            images.append(image_info)
                            
                            # Add image marker to text
                            page_text += f"\n[IMAGE:{image_info.id}]\n"
                        
                        pix = None  # Clean up
                        
                    except Exception as e:
                        logger.warning(f"Could not extract image {img_index} from page {page_num}: {e}")
                
                text_content += page_text
                current_position += len(page_text)
            
            doc.close()
            return text_content, images
            
        except Exception as e:
            logger.error(f"Error extracting PDF content with images: {e}")
            return "", []
    
    def _extract_docx_content_with_images(self, file_path: Path) -> Tuple[str, List[ImageInfo]]:
        """Extract text and images from DOCX with positioning."""
        if not DocxDocument:
            logger.error("python-docx not available for DOCX processing")
            return "", []
        
        try:
            doc = DocxDocument(str(file_path))
            text_content = ""
            images = []
            current_position = 0
            
            # Process paragraphs and extract images
            for para in doc.paragraphs:
                para_text = para.text
                
                # Check for images in this paragraph
                for run in para.runs:
                    # Check if this run contains images
                    for inline_shape in run.element.xpath('.//a:blip'):
                        try:
                            # Get the image relationship
                            embed_id = inline_shape.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                            if embed_id:
                                # Get image data from the document
                                image_part = doc.part.related_parts[embed_id]
                                image_data = image_part.blob
                                
                                # Determine image format
                                image_format = "png"
                                if image_data.startswith(b'\xff\xd8'):
                                    image_format = "jpeg"
                                elif image_data.startswith(b'\x89PNG'):
                                    image_format = "png"
                                elif image_data.startswith(b'GIF'):
                                    image_format = "gif"
                                
                                # Convert to data URI
                                data_uri = f"data:image/{image_format};base64,{base64.b64encode(image_data).decode()}"
                                
                                # Get image dimensions if possible
                                width, height = 300, 200  # Default dimensions
                                try:
                                    if Image:
                                        with Image.open(io.BytesIO(image_data)) as img:
                                            width, height = img.size
                                except Exception:
                                    pass
                                
                                # Create image info
                                image_position = current_position + len(para_text)
                                image_info = ImageInfo(
                                    data_uri=data_uri,
                                    width=width,
                                    height=height,
                                    position=image_position,
                                    alt_text=f"Image from paragraph"
                                )
                                images.append(image_info)
                                
                                # Add image marker to paragraph text
                                para_text += f"\n[IMAGE:{image_info.id}]\n"
                        
                        except Exception as e:
                            logger.warning(f"Could not extract image from paragraph: {e}")
                
                text_content += para_text + "\n\n"
                current_position = len(text_content)
            
            # Process tables (similar to existing logic but with image support)
            for table in doc.tables:
                text_content += "\n[TABLE]\n"
                current_position = len(text_content)
                
                for row in table.rows:
                    row_texts = []
                    for cell in row.cells:
                        cell_text = ""
                        for cell_para in cell.paragraphs:
                            cell_text += cell_para.text + "\n"
                        row_texts.append(cell_text.strip())
                    
                    table_row = " | ".join(row_texts) + "\n"
                    text_content += table_row
                    current_position = len(text_content)
                
                text_content += "[/TABLE]\n\n"
                current_position = len(text_content)
            
            return text_content, images
            
        except Exception as e:
            logger.error(f"Error extracting DOCX content with images: {e}")
            return "", []
    
    def create_html_with_embedded_images(self, text_content: str, images: List[ImageInfo]) -> str:
        """Create HTML content with embedded images at correct positions.
        
        Args:
            text_content: Text content with image markers
            images: List of image information
            
        Returns:
            HTML content with embedded images
        """
        try:
            # Create a mapping of image IDs to image info
            image_map = {img.id: img for img in images}
            
            # Start building HTML
            html_parts = ['<html><body>']
            
            # Process text and replace image markers with actual images
            lines = text_content.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # Check if this line is an image marker
                if line.startswith('[IMAGE:') and line.endswith(']'):
                    image_id = line[7:-1]  # Extract ID from [IMAGE:id]
                    if image_id in image_map:
                        img_info = image_map[image_id]
                        
                        # Create HTML img tag with proper sizing
                        max_width = min(img_info.width, 800)  # Limit max width
                        max_height = min(img_info.height, 600)  # Limit max height
                        
                        img_html = f'''<div style="text-align: center; margin: 10px 0;">
    <img src="{img_info.data_uri}" 
         alt="{img_info.alt_text}" 
         style="max-width: {max_width}px; max-height: {max_height}px; width: auto; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
         title="{img_info.alt_text}" />
    {f'<p style="font-style: italic; color: #666; margin-top: 5px;">{img_info.caption}</p>' if img_info.caption else ''}
</div>'''
                        html_parts.append(img_html)
                    continue
                
                # Handle regular text content
                if not line:
                    html_parts.append('<br>')
                    continue
                
                # Handle table markers (existing logic)
                if line == '[TABLE]':
                    html_parts.append('<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">')
                    continue
                elif line == '[/TABLE]':
                    html_parts.append('</table>')
                    continue
                elif '|' in line and not line.startswith('#'):
                    # Table row
                    cells = [cell.strip() for cell in line.split('|')]
                    html_parts.append('<tr>')
                    for cell in cells:
                        escaped_cell = self._escape_html(cell)
                        html_parts.append(f'<td style="padding: 8px; border: 1px solid #ccc;">{escaped_cell}</td>')
                    html_parts.append('</tr>')
                    continue
                
                # Handle headings
                if line.startswith('######'):
                    html_parts.append(f'<h6>{self._escape_html(line[6:].strip())}</h6>')
                elif line.startswith('#####'):
                    html_parts.append(f'<h5>{self._escape_html(line[5:].strip())}</h5>')
                elif line.startswith('####'):
                    html_parts.append(f'<h4>{self._escape_html(line[4:].strip())}</h4>')
                elif line.startswith('###'):
                    html_parts.append(f'<h3>{self._escape_html(line[3:].strip())}</h3>')
                elif line.startswith('##'):
                    html_parts.append(f'<h2>{self._escape_html(line[2:].strip())}</h2>')
                elif line.startswith('#'):
                    html_parts.append(f'<h1>{self._escape_html(line[1:].strip())}</h1>')
                else:
                    # Regular paragraph
                    escaped_line = self._escape_html(line)
                    escaped_line = escaped_line.replace('\n', '<br>')
                    html_parts.append(f'<p>{escaped_line}</p>')
            
            html_parts.append('</body></html>')
            return '\n'.join(html_parts)
            
        except Exception as e:
            logger.error(f"Error creating HTML with embedded images: {e}")
            # Fallback to text without images
            return self._escape_html(text_content)
    
    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters."""
        if not text:
            return ""
            
        replacements = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;'
        }
        
        for char, escape in replacements.items():
            text = text.replace(char, escape)
            
        return text
