"""
Knowledge Base Repository

Provides comprehensive data persistence for the Knowledge Base system
using SQLite with advanced indexing and full-text search capabilities.
"""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from loguru import logger

from knowledge_base.models.kb_annotation import KBAnnotation, KBAnnotationType, KBAnnotationPriority
from knowledge_base.models.kb_attachment import KBAttachment, KBAttachmentType
from knowledge_base.models.kb_collection import KBCollection, KBCollectionType, KBCollectionStatus
from knowledge_base.models.kb_document import KBDocument, KBDocumentType, KBDocumentStatus
from knowledge_base.models.kb_reference import KBReference, KBReferenceType
from knowledge_base.models.kb_section import KBSection
from knowledge_base.models.kb_version import KBVersion, KBVersionType


class KnowledgeBaseRepository:
    """
    Comprehensive repository for Knowledge Base data persistence.
    
    Provides advanced database operations with full-text search,
    indexing, and relationship management for the Knowledge Base system.
    """
    
    def __init__(self, db_path: Union[str, Path] = "data/knowledge_base.db"):
        """
        Initialize the Knowledge Base repository.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database schema
        self._init_database()
        logger.info(f"Knowledge Base repository initialized with database: {self.db_path}")
        
    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection with proper configuration."""
        conn = sqlite3.connect(
            self.db_path,
            detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES
        )
        conn.row_factory = sqlite3.Row
        
        # Enable foreign key constraints
        conn.execute("PRAGMA foreign_keys = ON")
        
        # Optimize for better performance
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        conn.execute("PRAGMA temp_store = MEMORY")
        conn.execute("PRAGMA mmap_size = 268435456")  # 256MB
        
        return conn
        
    def _init_database(self) -> None:
        """Initialize database schema with all tables and indexes."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Create all tables
            self._create_collections_table(cursor)
            self._create_documents_table(cursor)
            self._create_sections_table(cursor)
            self._create_references_table(cursor)
            self._create_attachments_table(cursor)
            self._create_versions_table(cursor)
            self._create_annotations_table(cursor)
            
            # Create junction tables
            self._create_document_collections_table(cursor)
            self._create_document_tags_table(cursor)
            
            # Create full-text search tables
            self._create_search_tables(cursor)
            
            # Create indexes
            self._create_indexes(cursor)
            
            # Create triggers
            self._create_triggers(cursor)
            
            conn.commit()
            logger.info("Knowledge Base database schema initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database schema: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
            
    def _create_collections_table(self, cursor: sqlite3.Cursor) -> None:
        """Create collections table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_collections (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                parent_collection_id TEXT,
                path TEXT NOT NULL,
                depth_level INTEGER NOT NULL DEFAULT 0,
                description TEXT,
                summary TEXT,
                collection_type TEXT NOT NULL DEFAULT 'folder',
                status TEXT NOT NULL DEFAULT 'active',
                tags TEXT DEFAULT '[]',
                keywords TEXT DEFAULT '[]',
                display_order INTEGER NOT NULL DEFAULT 0,
                icon TEXT,
                color TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                is_public BOOLEAN NOT NULL DEFAULT 1,
                is_system BOOLEAN NOT NULL DEFAULT 0,
                document_count INTEGER NOT NULL DEFAULT 0,
                total_word_count INTEGER NOT NULL DEFAULT 0,
                subcollection_count INTEGER NOT NULL DEFAULT 0,
                auto_organize BOOLEAN NOT NULL DEFAULT 0,
                allow_duplicates BOOLEAN NOT NULL DEFAULT 1,
                sort_order TEXT NOT NULL DEFAULT 'created_desc',
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (parent_collection_id) REFERENCES kb_collections(id) ON DELETE CASCADE
            )
        """)
        
    def _create_documents_table(self, cursor: sqlite3.Cursor) -> None:
        """Create documents table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_documents (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL DEFAULT '',
                summary TEXT,
                document_type TEXT NOT NULL DEFAULT 'article',
                status TEXT NOT NULL DEFAULT 'draft',
                collection_id TEXT,
                tags TEXT DEFAULT '[]',
                keywords TEXT DEFAULT '[]',
                author TEXT,
                source TEXT,
                language TEXT NOT NULL DEFAULT 'en',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                published_at TIMESTAMP,
                word_count INTEGER NOT NULL DEFAULT 0,
                reading_time_minutes INTEGER NOT NULL DEFAULT 0,
                parent_document_id TEXT,
                related_document_ids TEXT DEFAULT '[]',
                search_vector TEXT,
                importance_score REAL NOT NULL DEFAULT 1.0,
                is_public BOOLEAN NOT NULL DEFAULT 1,
                permissions TEXT DEFAULT '{}',
                version INTEGER NOT NULL DEFAULT 1,
                is_latest_version BOOLEAN NOT NULL DEFAULT 1,
                external_links TEXT DEFAULT '[]',
                has_images BOOLEAN NOT NULL DEFAULT 0,
                has_tables BOOLEAN NOT NULL DEFAULT 0,
                has_formulas BOOLEAN NOT NULL DEFAULT 0,
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (collection_id) REFERENCES kb_collections(id) ON DELETE SET NULL,
                FOREIGN KEY (parent_document_id) REFERENCES kb_documents(id) ON DELETE SET NULL
            )
        """)
        
    def _create_sections_table(self, cursor: sqlite3.Cursor) -> None:
        """Create sections table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_sections (
                id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                parent_section_id TEXT,
                order_index INTEGER NOT NULL DEFAULT 0,
                depth_level INTEGER NOT NULL DEFAULT 1,
                title TEXT NOT NULL,
                content TEXT NOT NULL DEFAULT '',
                section_type TEXT NOT NULL DEFAULT 'content',
                tags TEXT DEFAULT '[]',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                word_count INTEGER NOT NULL DEFAULT 0,
                is_visible BOOLEAN NOT NULL DEFAULT 1,
                is_collapsible BOOLEAN NOT NULL DEFAULT 1,
                has_images BOOLEAN NOT NULL DEFAULT 0,
                has_tables BOOLEAN NOT NULL DEFAULT 0,
                has_formulas BOOLEAN NOT NULL DEFAULT 0,
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (parent_section_id) REFERENCES kb_sections(id) ON DELETE CASCADE
            )
        """)
        
    def _create_references_table(self, cursor: sqlite3.Cursor) -> None:
        """Create references table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_references (
                id TEXT PRIMARY KEY,
                source_document_id TEXT NOT NULL,
                target_document_id TEXT,
                reference_type TEXT NOT NULL,
                title TEXT,
                description TEXT,
                external_url TEXT,
                external_title TEXT,
                external_author TEXT,
                external_publication TEXT,
                page_number TEXT,
                quote_text TEXT,
                context TEXT,
                source_section_id TEXT,
                source_position INTEGER,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                is_verified BOOLEAN NOT NULL DEFAULT 0,
                is_broken BOOLEAN NOT NULL DEFAULT 0,
                last_checked TIMESTAMP,
                display_text TEXT,
                citation_style TEXT NOT NULL DEFAULT 'default',
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (source_document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (target_document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (source_section_id) REFERENCES kb_sections(id) ON DELETE SET NULL
            )
        """)
        
    def _create_attachments_table(self, cursor: sqlite3.Cursor) -> None:
        """Create attachments table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_attachments (
                id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                filename TEXT NOT NULL,
                stored_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                attachment_type TEXT NOT NULL,
                mime_type TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                file_hash TEXT,
                title TEXT,
                description TEXT,
                alt_text TEXT,
                section_id TEXT,
                position_index INTEGER,
                width INTEGER,
                height INTEGER,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                uploaded_by TEXT,
                is_embedded BOOLEAN NOT NULL DEFAULT 0,
                is_referenced BOOLEAN NOT NULL DEFAULT 1,
                reference_count INTEGER NOT NULL DEFAULT 0,
                is_public BOOLEAN NOT NULL DEFAULT 1,
                requires_authentication BOOLEAN NOT NULL DEFAULT 0,
                is_processed BOOLEAN NOT NULL DEFAULT 1,
                processing_error TEXT,
                thumbnail_path TEXT,
                preview_path TEXT,
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (section_id) REFERENCES kb_sections(id) ON DELETE SET NULL
            )
        """)
        
    def _create_versions_table(self, cursor: sqlite3.Cursor) -> None:
        """Create versions table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_versions (
                id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                version_number TEXT NOT NULL,
                version_type TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                summary TEXT,
                change_summary TEXT,
                change_notes TEXT,
                change_type TEXT NOT NULL DEFAULT 'content',
                created_by TEXT,
                author_email TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                parent_version_id TEXT,
                merged_from_version_ids TEXT DEFAULT '[]',
                is_current BOOLEAN NOT NULL DEFAULT 0,
                is_published BOOLEAN NOT NULL DEFAULT 0,
                is_archived BOOLEAN NOT NULL DEFAULT 0,
                is_protected BOOLEAN NOT NULL DEFAULT 0,
                word_count INTEGER NOT NULL DEFAULT 0,
                tags TEXT DEFAULT '[]',
                lines_added INTEGER NOT NULL DEFAULT 0,
                lines_removed INTEGER NOT NULL DEFAULT 0,
                lines_modified INTEGER NOT NULL DEFAULT 0,
                content_hash TEXT,
                content_size INTEGER NOT NULL DEFAULT 0,
                branch_name TEXT,
                merge_commit_message TEXT,
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (parent_version_id) REFERENCES kb_versions(id) ON DELETE SET NULL
            )
        """)
        
    def _create_annotations_table(self, cursor: sqlite3.Cursor) -> None:
        """Create annotations table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_annotations (
                id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                section_id TEXT,
                start_position INTEGER NOT NULL,
                end_position INTEGER NOT NULL,
                selected_text TEXT,
                annotation_type TEXT NOT NULL,
                content TEXT NOT NULL DEFAULT '',
                title TEXT,
                color TEXT NOT NULL DEFAULT '#ffff00',
                style TEXT NOT NULL DEFAULT 'highlight',
                priority TEXT NOT NULL DEFAULT 'normal',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                author_email TEXT,
                is_resolved BOOLEAN NOT NULL DEFAULT 0,
                is_private BOOLEAN NOT NULL DEFAULT 0,
                is_sticky BOOLEAN NOT NULL DEFAULT 0,
                parent_annotation_id TEXT,
                reply_count INTEGER NOT NULL DEFAULT 0,
                tags TEXT DEFAULT '[]',
                category TEXT,
                reminder_date TIMESTAMP,
                due_date TIMESTAMP,
                shared_with TEXT DEFAULT '[]',
                permissions TEXT DEFAULT '{}',
                anchor_context TEXT,
                xpath TEXT,
                custom_metadata TEXT DEFAULT '{}',
                FOREIGN KEY (document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (section_id) REFERENCES kb_sections(id) ON DELETE SET NULL,
                FOREIGN KEY (parent_annotation_id) REFERENCES kb_annotations(id) ON DELETE CASCADE
            )
        """)
        
    def _create_document_collections_table(self, cursor: sqlite3.Cursor) -> None:
        """Create document-collections junction table."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_document_collections (
                document_id TEXT NOT NULL,
                collection_id TEXT NOT NULL,
                added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                added_by TEXT,
                PRIMARY KEY (document_id, collection_id),
                FOREIGN KEY (document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (collection_id) REFERENCES kb_collections(id) ON DELETE CASCADE
            )
        """)
        
    def _create_document_tags_table(self, cursor: sqlite3.Cursor) -> None:
        """Create document tags table for better tag management."""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kb_document_tags (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id TEXT NOT NULL,
                tag TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
                UNIQUE(document_id, tag)
            )
        """)
        
    def _create_search_tables(self, cursor: sqlite3.Cursor) -> None:
        """Create full-text search tables."""
        # Main content search
        cursor.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS kb_documents_fts USING fts5(
                document_id UNINDEXED,
                title,
                content,
                summary,
                tags,
                keywords,
                author,
                source
            )
        """)
        
        # Section search
        cursor.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS kb_sections_fts USING fts5(
                section_id UNINDEXED,
                document_id UNINDEXED,
                title,
                content,
                tags
            )
        """)
        
        # Collection search
        cursor.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS kb_collections_fts USING fts5(
                collection_id UNINDEXED,
                name,
                description,
                summary,
                tags,
                keywords
            )
        """)
        
    def _create_indexes(self, cursor: sqlite3.Cursor) -> None:
        """Create database indexes for performance."""
        # Collections indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_collections_parent ON kb_collections(parent_collection_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_collections_type ON kb_collections(collection_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_collections_status ON kb_collections(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_collections_path ON kb_collections(path)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_collections_created ON kb_collections(created_at)")
        
        # Documents indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_collection ON kb_documents(collection_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_type ON kb_documents(document_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_status ON kb_documents(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_author ON kb_documents(author)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_created ON kb_documents(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_updated ON kb_documents(updated_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_published ON kb_documents(published_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_parent ON kb_documents(parent_document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_version ON kb_documents(version)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_latest ON kb_documents(is_latest_version)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_public ON kb_documents(is_public)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_documents_importance ON kb_documents(importance_score)")
        
        # Sections indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_sections_document ON kb_sections(document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_sections_parent ON kb_sections(parent_section_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_sections_order ON kb_sections(order_index)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_sections_type ON kb_sections(section_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_sections_created ON kb_sections(created_at)")
        
        # References indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_references_source ON kb_references(source_document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_references_target ON kb_references(target_document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_references_type ON kb_references(reference_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_references_created ON kb_references(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_references_verified ON kb_references(is_verified)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_references_broken ON kb_references(is_broken)")
        
        # Attachments indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_document ON kb_attachments(document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_section ON kb_attachments(section_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_type ON kb_attachments(attachment_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_mime ON kb_attachments(mime_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_hash ON kb_attachments(file_hash)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_embedded ON kb_attachments(is_embedded)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_attachments_created ON kb_attachments(created_at)")
        
        # Versions indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_document ON kb_versions(document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_number ON kb_versions(version_number)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_type ON kb_versions(version_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_current ON kb_versions(is_current)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_published ON kb_versions(is_published)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_created ON kb_versions(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_versions_parent ON kb_versions(parent_version_id)")
        
        # Annotations indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_document ON kb_annotations(document_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_section ON kb_annotations(section_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_type ON kb_annotations(annotation_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_position ON kb_annotations(start_position, end_position)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_created ON kb_annotations(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_author ON kb_annotations(created_by)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_resolved ON kb_annotations(is_resolved)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_priority ON kb_annotations(priority)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_parent ON kb_annotations(parent_annotation_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_due ON kb_annotations(due_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_annotations_reminder ON kb_annotations(reminder_date)")
        
        # Tag indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_document_tags_tag ON kb_document_tags(tag)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_kb_document_tags_created ON kb_document_tags(created_at)")
        
    def _create_triggers(self, cursor: sqlite3.Cursor) -> None:
        """Create database triggers for maintaining data consistency."""
        # Update document search index when document changes
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_document_fts_insert
            AFTER INSERT ON kb_documents
            BEGIN
                INSERT INTO kb_documents_fts(document_id, title, content, summary, tags, keywords, author, source)
                VALUES(NEW.id, NEW.title, NEW.content, NEW.summary, NEW.tags, NEW.keywords, NEW.author, NEW.source);
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_document_fts_update
            AFTER UPDATE ON kb_documents
            BEGIN
                UPDATE kb_documents_fts SET 
                    title = NEW.title,
                    content = NEW.content,
                    summary = NEW.summary,
                    tags = NEW.tags,
                    keywords = NEW.keywords,
                    author = NEW.author,
                    source = NEW.source
                WHERE document_id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_document_fts_delete
            AFTER DELETE ON kb_documents
            BEGIN
                DELETE FROM kb_documents_fts WHERE document_id = OLD.id;
            END
        """)
        
        # Update section search index when section changes
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_section_fts_insert
            AFTER INSERT ON kb_sections
            BEGIN
                INSERT INTO kb_sections_fts(section_id, document_id, title, content, tags)
                VALUES(NEW.id, NEW.document_id, NEW.title, NEW.content, NEW.tags);
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_section_fts_update
            AFTER UPDATE ON kb_sections
            BEGIN
                UPDATE kb_sections_fts SET 
                    title = NEW.title,
                    content = NEW.content,
                    tags = NEW.tags
                WHERE section_id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_section_fts_delete
            AFTER DELETE ON kb_sections
            BEGIN
                DELETE FROM kb_sections_fts WHERE section_id = OLD.id;
            END
        """)
        
        # Update collection search index when collection changes
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_collection_fts_insert
            AFTER INSERT ON kb_collections
            BEGIN
                INSERT INTO kb_collections_fts(collection_id, name, description, summary, tags, keywords)
                VALUES(NEW.id, NEW.name, NEW.description, NEW.summary, NEW.tags, NEW.keywords);
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_collection_fts_update
            AFTER UPDATE ON kb_collections
            BEGIN
                UPDATE kb_collections_fts SET 
                    name = NEW.name,
                    description = NEW.description,
                    summary = NEW.summary,
                    tags = NEW.tags,
                    keywords = NEW.keywords
                WHERE collection_id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_collection_fts_delete
            AFTER DELETE ON kb_collections
            BEGIN
                DELETE FROM kb_collections_fts WHERE collection_id = OLD.id;
            END
        """)
        
        # Update timestamps
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_document_timestamp
            AFTER UPDATE ON kb_documents
            WHEN NEW.updated_at = OLD.updated_at
            BEGIN
                UPDATE kb_documents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_collection_timestamp
            AFTER UPDATE ON kb_collections
            WHEN NEW.updated_at = OLD.updated_at
            BEGIN
                UPDATE kb_collections SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_section_timestamp
            AFTER UPDATE ON kb_sections
            WHEN NEW.updated_at = OLD.updated_at
            BEGIN
                UPDATE kb_sections SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_annotation_timestamp
            AFTER UPDATE ON kb_annotations
            WHEN NEW.updated_at = OLD.updated_at
            BEGIN
                UPDATE kb_annotations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
    # Collection CRUD operations
    def save_collection(self, collection: KBCollection) -> bool:
        """Save a collection to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if collection exists
            cursor.execute("SELECT id FROM kb_collections WHERE id = ?", (collection.id,))
            exists = cursor.fetchone() is not None
            
            collection_data = {
                'id': collection.id,
                'name': collection.name,
                'parent_collection_id': collection.parent_collection_id,
                'path': collection.path,
                'depth_level': collection.depth_level,
                'description': collection.description,
                'summary': collection.summary,
                'collection_type': collection.collection_type.value,
                'status': collection.status.value,
                'tags': json.dumps(collection.tags),
                'keywords': json.dumps(collection.keywords),
                'display_order': collection.display_order,
                'icon': collection.icon,
                'color': collection.color,
                'created_at': collection.created_at.isoformat(),
                'updated_at': collection.updated_at.isoformat(),
                'created_by': collection.created_by,
                'is_public': collection.is_public,
                'is_system': collection.is_system,
                'document_count': collection.document_count,
                'total_word_count': collection.total_word_count,
                'subcollection_count': collection.subcollection_count,
                'auto_organize': collection.auto_organize,
                'allow_duplicates': collection.allow_duplicates,
                'sort_order': collection.sort_order,
                'custom_metadata': json.dumps(collection.custom_metadata)
            }
            
            if exists:
                # Update existing collection
                set_clause = ", ".join([f"{k} = ?" for k in collection_data.keys() if k != 'id'])
                values = [v for k, v in collection_data.items() if k != 'id'] + [collection.id]
                cursor.execute(f"UPDATE kb_collections SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new collection
                columns = ", ".join(collection_data.keys())
                placeholders = ", ".join(["?"] * len(collection_data))
                cursor.execute(
                    f"INSERT INTO kb_collections ({columns}) VALUES ({placeholders})",
                    list(collection_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving collection {collection.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_collection_by_id(self, collection_id: str) -> Optional[KBCollection]:
        """Get a collection by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_collections WHERE id = ?", (collection_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_collection(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting collection {collection_id}: {e}")
            return None
        finally:
            conn.close()
            
    def _row_to_collection(self, row: sqlite3.Row) -> KBCollection:
        """Convert database row to KBCollection object."""
        return KBCollection(
            id=row['id'],
            name=row['name'],
            parent_collection_id=row['parent_collection_id'],
            path=row['path'],
            depth_level=row['depth_level'],
            description=row['description'],
            summary=row['summary'],
            collection_type=KBCollectionType(row['collection_type']),
            status=KBCollectionStatus(row['status']),
            tags=json.loads(row['tags']) if row['tags'] else [],
            keywords=json.loads(row['keywords']) if row['keywords'] else [],
            display_order=row['display_order'],
            icon=row['icon'],
            color=row['color'],
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            created_by=row['created_by'],
            is_public=bool(row['is_public']),
            is_system=bool(row['is_system']),
            document_count=row['document_count'],
            total_word_count=row['total_word_count'],
            subcollection_count=row['subcollection_count'],
            auto_organize=bool(row['auto_organize']),
            allow_duplicates=bool(row['allow_duplicates']),
            sort_order=row['sort_order'],
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
        
    # Document CRUD operations  
    def save_document(self, document: KBDocument) -> bool:
        """Save a document to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if document exists
            cursor.execute("SELECT id FROM kb_documents WHERE id = ?", (document.id,))
            exists = cursor.fetchone() is not None
            
            document_data = {
                'id': document.id,
                'title': document.title,
                'content': document.content,
                'summary': document.summary,
                'document_type': document.document_type.value,
                'status': document.status.value,
                'collection_id': document.collection_id,
                'tags': json.dumps(document.tags),
                'keywords': json.dumps(document.keywords),
                'author': document.author,
                'source': document.source,
                'language': document.language,
                'created_at': document.created_at.isoformat(),
                'updated_at': document.updated_at.isoformat(),
                'published_at': document.published_at.isoformat() if document.published_at else None,
                'word_count': document.word_count,
                'reading_time_minutes': document.reading_time_minutes,
                'parent_document_id': document.parent_document_id,
                'related_document_ids': json.dumps(document.related_document_ids),
                'search_vector': document.search_vector,
                'importance_score': document.importance_score,
                'is_public': document.is_public,
                'permissions': json.dumps(document.permissions),
                'version': document.version,
                'is_latest_version': document.is_latest_version,
                'external_links': json.dumps(document.external_links),
                'has_images': document.has_images,
                'has_tables': document.has_tables,
                'has_formulas': document.has_formulas,
                'custom_metadata': json.dumps(document.custom_metadata)
            }
            
            if exists:
                # Update existing document
                set_clause = ", ".join([f"{k} = ?" for k in document_data.keys() if k != 'id'])
                values = [v for k, v in document_data.items() if k != 'id'] + [document.id]
                cursor.execute(f"UPDATE kb_documents SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new document
                columns = ", ".join(document_data.keys())
                placeholders = ", ".join(["?"] * len(document_data))
                cursor.execute(
                    f"INSERT INTO kb_documents ({columns}) VALUES ({placeholders})",
                    list(document_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving document {document.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_document_by_id(self, document_id: str) -> Optional[KBDocument]:
        """Get a document by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_documents WHERE id = ?", (document_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_document(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting document {document_id}: {e}")
            return None
        finally:
            conn.close()
            
    def _row_to_document(self, row: sqlite3.Row) -> KBDocument:
        """Convert database row to KBDocument object."""
        return KBDocument(
            id=row['id'],
            title=row['title'],
            content=row['content'],
            summary=row['summary'],
            document_type=KBDocumentType(row['document_type']),
            status=KBDocumentStatus(row['status']),
            collection_id=row['collection_id'],
            tags=json.loads(row['tags']) if row['tags'] else [],
            keywords=json.loads(row['keywords']) if row['keywords'] else [],
            author=row['author'],
            source=row['source'],
            language=row['language'],
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            published_at=datetime.fromisoformat(row['published_at']) if row['published_at'] else None,
            word_count=row['word_count'],
            reading_time_minutes=row['reading_time_minutes'],
            parent_document_id=row['parent_document_id'],
            related_document_ids=json.loads(row['related_document_ids']) if row['related_document_ids'] else [],
            search_vector=row['search_vector'],
            importance_score=row['importance_score'],
            is_public=bool(row['is_public']),
            permissions=json.loads(row['permissions']) if row['permissions'] else {},
            version=row['version'],
            is_latest_version=bool(row['is_latest_version']),
            external_links=json.loads(row['external_links']) if row['external_links'] else [],
            has_images=bool(row['has_images']),
            has_tables=bool(row['has_tables']),
            has_formulas=bool(row['has_formulas']),
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
        
    def search_documents(self, query: str, limit: int = 50, offset: int = 0) -> Tuple[List[KBDocument], int]:
        """Search documents using full-text search."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Full-text search query
            search_query = f"""
                SELECT d.*, rank 
                FROM kb_documents d
                JOIN (
                    SELECT document_id, rank
                    FROM kb_documents_fts
                    WHERE kb_documents_fts MATCH ?
                    ORDER BY rank
                    LIMIT ? OFFSET ?
                ) fts ON d.id = fts.document_id
                ORDER BY fts.rank
            """
            
            cursor.execute(search_query, (query, limit, offset))
            documents = [self._row_to_document(row) for row in cursor.fetchall()]
            
            # Get total count
            cursor.execute(
                "SELECT COUNT(*) as count FROM kb_documents_fts WHERE kb_documents_fts MATCH ?",
                (query,)
            )
            total_count = cursor.fetchone()['count']
            
            return documents, total_count
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return [], 0
        finally:
            conn.close()
            
    def get_database_stats(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            stats = {}
            
            # Document statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_documents")
            stats['total_documents'] = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM kb_documents WHERE status = 'published'")
            stats['published_documents'] = cursor.fetchone()['count']
            
            cursor.execute("SELECT SUM(word_count) as total FROM kb_documents")
            stats['total_words'] = cursor.fetchone()['total'] or 0
            
            # Collection statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_collections")
            stats['total_collections'] = cursor.fetchone()['count']
            
            # Section statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_sections")
            stats['total_sections'] = cursor.fetchone()['count']
            
            # Reference statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_references")
            stats['total_references'] = cursor.fetchone()['count']
            
            # Attachment statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_attachments")
            stats['total_attachments'] = cursor.fetchone()['count']
            
            cursor.execute("SELECT SUM(file_size) as total FROM kb_attachments")
            stats['total_attachment_size'] = cursor.fetchone()['total'] or 0
            
            # Version statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_versions")
            stats['total_versions'] = cursor.fetchone()['count']
            
            # Annotation statistics
            cursor.execute("SELECT COUNT(*) as count FROM kb_annotations")
            stats['total_annotations'] = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM kb_annotations WHERE is_resolved = 0")
            stats['unresolved_annotations'] = cursor.fetchone()['count']
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}
        finally:
            conn.close()
    
    # Section CRUD operations
    def save_section(self, section: KBSection) -> bool:
        """Save a section to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if section exists
            cursor.execute("SELECT id FROM kb_sections WHERE id = ?", (section.id,))
            exists = cursor.fetchone() is not None
            
            section_data = {
                'id': section.id,
                'document_id': section.document_id,
                'parent_section_id': section.parent_section_id,
                'order_index': section.order_index,
                'depth_level': section.depth_level,
                'title': section.title,
                'content': section.content,
                'section_type': section.section_type,
                'tags': json.dumps(section.tags),
                'created_at': section.created_at.isoformat(),
                'updated_at': section.updated_at.isoformat(),
                'word_count': section.word_count,
                'is_visible': section.is_visible,
                'is_collapsible': section.is_collapsible,
                'has_images': section.has_images,
                'has_tables': section.has_tables,
                'has_formulas': section.has_formulas,
                'custom_metadata': json.dumps(section.custom_metadata)
            }
            
            if exists:
                # Update existing section
                set_clause = ", ".join([f"{k} = ?" for k in section_data.keys() if k != 'id'])
                values = [v for k, v in section_data.items() if k != 'id'] + [section.id]
                cursor.execute(f"UPDATE kb_sections SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new section
                columns = ", ".join(section_data.keys())
                placeholders = ", ".join(["?"] * len(section_data))
                cursor.execute(
                    f"INSERT INTO kb_sections ({columns}) VALUES ({placeholders})",
                    list(section_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving section {section.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_section_by_id(self, section_id: str) -> Optional[KBSection]:
        """Get a section by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_sections WHERE id = ?", (section_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_section(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting section {section_id}: {e}")
            return None
        finally:
            conn.close()
            
    def get_sections_by_document(self, document_id: str) -> List[KBSection]:
        """Get all sections for a document."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_sections WHERE document_id = ? ORDER BY order_index ASC",
                (document_id,)
            )
            return [self._row_to_section(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting sections for document {document_id}: {e}")
            return []
        finally:
            conn.close()
            
    def _row_to_section(self, row: sqlite3.Row) -> KBSection:
        """Convert database row to KBSection object."""
        return KBSection(
            id=row['id'],
            document_id=row['document_id'],
            parent_section_id=row['parent_section_id'],
            order_index=row['order_index'],
            depth_level=row['depth_level'],
            title=row['title'],
            content=row['content'],
            section_type=row['section_type'],
            tags=json.loads(row['tags']) if row['tags'] else [],
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            word_count=row['word_count'],
            is_visible=bool(row['is_visible']),
            is_collapsible=bool(row['is_collapsible']),
            has_images=bool(row['has_images']),
            has_tables=bool(row['has_tables']),
            has_formulas=bool(row['has_formulas']),
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
    
    # Reference CRUD operations
    def save_reference(self, reference: KBReference) -> bool:
        """Save a reference to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if reference exists
            cursor.execute("SELECT id FROM kb_references WHERE id = ?", (reference.id,))
            exists = cursor.fetchone() is not None
            
            reference_data = {
                'id': reference.id,
                'source_document_id': reference.source_document_id,
                'target_document_id': reference.target_document_id,
                'reference_type': reference.reference_type.value,
                'title': reference.title,
                'description': reference.description,
                'external_url': reference.external_url,
                'external_title': reference.external_title,
                'external_author': reference.external_author,
                'external_publication': reference.external_publication,
                'page_number': reference.page_number,
                'quote_text': reference.quote_text,
                'context': reference.context,
                'source_section_id': reference.source_section_id,
                'source_position': reference.source_position,
                'created_at': reference.created_at.isoformat(),
                'updated_at': reference.updated_at.isoformat(),
                'created_by': reference.created_by,
                'is_verified': reference.is_verified,
                'is_broken': reference.is_broken,
                'last_checked': reference.last_checked.isoformat() if reference.last_checked else None,
                'display_text': reference.display_text,
                'citation_style': reference.citation_style,
                'custom_metadata': json.dumps(reference.custom_metadata)
            }
            
            if exists:
                # Update existing reference
                set_clause = ", ".join([f"{k} = ?" for k in reference_data.keys() if k != 'id'])
                values = [v for k, v in reference_data.items() if k != 'id'] + [reference.id]
                cursor.execute(f"UPDATE kb_references SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new reference
                columns = ", ".join(reference_data.keys())
                placeholders = ", ".join(["?"] * len(reference_data))
                cursor.execute(
                    f"INSERT INTO kb_references ({columns}) VALUES ({placeholders})",
                    list(reference_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving reference {reference.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_reference_by_id(self, reference_id: str) -> Optional[KBReference]:
        """Get a reference by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_references WHERE id = ?", (reference_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_reference(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting reference {reference_id}: {e}")
            return None
        finally:
            conn.close()
            
    def get_references_by_document(self, document_id: str) -> List[KBReference]:
        """Get all references for a document."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_references WHERE source_document_id = ? ORDER BY created_at ASC",
                (document_id,)
            )
            return [self._row_to_reference(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting references for document {document_id}: {e}")
            return []
        finally:
            conn.close()
            
    def _row_to_reference(self, row: sqlite3.Row) -> KBReference:
        """Convert database row to KBReference object."""
        return KBReference(
            id=row['id'],
            source_document_id=row['source_document_id'],
            target_document_id=row['target_document_id'],
            reference_type=KBReferenceType(row['reference_type']),
            title=row['title'],
            description=row['description'],
            external_url=row['external_url'],
            external_title=row['external_title'],
            external_author=row['external_author'],
            external_publication=row['external_publication'],
            page_number=row['page_number'],
            quote_text=row['quote_text'],
            context=row['context'],
            source_section_id=row['source_section_id'],
            source_position=row['source_position'],
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            created_by=row['created_by'],
            is_verified=bool(row['is_verified']),
            is_broken=bool(row['is_broken']),
            last_checked=datetime.fromisoformat(row['last_checked']) if row['last_checked'] else None,
            display_text=row['display_text'],
            citation_style=row['citation_style'],
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
    
    # Attachment CRUD operations
    def save_attachment(self, attachment: KBAttachment) -> bool:
        """Save an attachment to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if attachment exists
            cursor.execute("SELECT id FROM kb_attachments WHERE id = ?", (attachment.id,))
            exists = cursor.fetchone() is not None
            
            attachment_data = {
                'id': attachment.id,
                'document_id': attachment.document_id,
                'filename': attachment.filename,
                'stored_filename': attachment.stored_filename,
                'file_path': attachment.file_path,
                'attachment_type': attachment.attachment_type.value,
                'mime_type': attachment.mime_type,
                'file_size': attachment.file_size,
                'file_hash': attachment.file_hash,
                'title': attachment.title,
                'description': attachment.description,
                'alt_text': attachment.alt_text,
                'section_id': attachment.section_id,
                'position_index': attachment.position_index,
                'width': attachment.width,
                'height': attachment.height,
                'created_at': attachment.created_at.isoformat(),
                'updated_at': attachment.updated_at.isoformat(),
                'uploaded_by': attachment.uploaded_by,
                'is_embedded': attachment.is_embedded,
                'is_referenced': attachment.is_referenced,
                'reference_count': attachment.reference_count,
                'is_public': attachment.is_public,
                'requires_authentication': attachment.requires_authentication,
                'is_processed': attachment.is_processed,
                'processing_error': attachment.processing_error,
                'thumbnail_path': attachment.thumbnail_path,
                'preview_path': attachment.preview_path,
                'custom_metadata': json.dumps(attachment.custom_metadata)
            }
            
            if exists:
                # Update existing attachment
                set_clause = ", ".join([f"{k} = ?" for k in attachment_data.keys() if k != 'id'])
                values = [v for k, v in attachment_data.items() if k != 'id'] + [attachment.id]
                cursor.execute(f"UPDATE kb_attachments SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new attachment
                columns = ", ".join(attachment_data.keys())
                placeholders = ", ".join(["?"] * len(attachment_data))
                cursor.execute(
                    f"INSERT INTO kb_attachments ({columns}) VALUES ({placeholders})",
                    list(attachment_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving attachment {attachment.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_attachment_by_id(self, attachment_id: str) -> Optional[KBAttachment]:
        """Get an attachment by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_attachments WHERE id = ?", (attachment_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_attachment(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting attachment {attachment_id}: {e}")
            return None
        finally:
            conn.close()
            
    def get_attachments_by_document(self, document_id: str) -> List[KBAttachment]:
        """Get all attachments for a document."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_attachments WHERE document_id = ? ORDER BY position_index ASC",
                (document_id,)
            )
            return [self._row_to_attachment(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting attachments for document {document_id}: {e}")
            return []
        finally:
            conn.close()
            
    def _row_to_attachment(self, row: sqlite3.Row) -> KBAttachment:
        """Convert database row to KBAttachment object."""
        return KBAttachment(
            id=row['id'],
            document_id=row['document_id'],
            filename=row['filename'],
            stored_filename=row['stored_filename'],
            file_path=row['file_path'],
            attachment_type=KBAttachmentType(row['attachment_type']),
            mime_type=row['mime_type'],
            file_size=row['file_size'],
            file_hash=row['file_hash'],
            title=row['title'],
            description=row['description'],
            alt_text=row['alt_text'],
            section_id=row['section_id'],
            position_index=row['position_index'],
            width=row['width'],
            height=row['height'],
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            uploaded_by=row['uploaded_by'],
            is_embedded=bool(row['is_embedded']),
            is_referenced=bool(row['is_referenced']),
            reference_count=row['reference_count'],
            is_public=bool(row['is_public']),
            requires_authentication=bool(row['requires_authentication']),
            is_processed=bool(row['is_processed']),
            processing_error=row['processing_error'],
            thumbnail_path=row['thumbnail_path'],
            preview_path=row['preview_path'],
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
    
    # Version CRUD operations
    def save_version(self, version: KBVersion) -> bool:
        """Save a version to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if version exists
            cursor.execute("SELECT id FROM kb_versions WHERE id = ?", (version.id,))
            exists = cursor.fetchone() is not None
            
            version_data = {
                'id': version.id,
                'document_id': version.document_id,
                'version_number': version.version_number,
                'version_type': version.version_type.value,
                'title': version.title,
                'content': version.content,
                'summary': version.summary,
                'change_summary': version.change_summary,
                'change_notes': version.change_notes,
                'change_type': version.change_type,
                'created_by': version.created_by,
                'author_email': version.author_email,
                'created_at': version.created_at.isoformat(),
                'parent_version_id': version.parent_version_id,
                'merged_from_version_ids': json.dumps(version.merged_from_version_ids),
                'is_current': version.is_current,
                'is_published': version.is_published,
                'is_archived': version.is_archived,
                'is_protected': version.is_protected,
                'word_count': version.word_count,
                'tags': json.dumps(version.tags),
                'lines_added': version.lines_added,
                'lines_removed': version.lines_removed,
                'lines_modified': version.lines_modified,
                'content_hash': version.content_hash,
                'content_size': version.content_size,
                'branch_name': version.branch_name,
                'merge_commit_message': version.merge_commit_message,
                'custom_metadata': json.dumps(version.custom_metadata)
            }
            
            if exists:
                # Update existing version
                set_clause = ", ".join([f"{k} = ?" for k in version_data.keys() if k != 'id'])
                values = [v for k, v in version_data.items() if k != 'id'] + [version.id]
                cursor.execute(f"UPDATE kb_versions SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new version
                columns = ", ".join(version_data.keys())
                placeholders = ", ".join(["?"] * len(version_data))
                cursor.execute(
                    f"INSERT INTO kb_versions ({columns}) VALUES ({placeholders})",
                    list(version_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving version {version.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_version_by_id(self, version_id: str) -> Optional[KBVersion]:
        """Get a version by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_versions WHERE id = ?", (version_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_version(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting version {version_id}: {e}")
            return None
        finally:
            conn.close()
            
    def get_versions_by_document(self, document_id: str) -> List[KBVersion]:
        """Get all versions for a document."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_versions WHERE document_id = ? ORDER BY created_at DESC",
                (document_id,)
            )
            return [self._row_to_version(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting versions for document {document_id}: {e}")
            return []
        finally:
            conn.close()
            
    def _row_to_version(self, row: sqlite3.Row) -> KBVersion:
        """Convert database row to KBVersion object."""
        return KBVersion(
            id=row['id'],
            document_id=row['document_id'],
            version_number=row['version_number'],
            version_type=KBVersionType(row['version_type']),
            title=row['title'],
            content=row['content'],
            summary=row['summary'],
            change_summary=row['change_summary'],
            change_notes=row['change_notes'],
            change_type=row['change_type'],
            created_by=row['created_by'],
            author_email=row['author_email'],
            created_at=datetime.fromisoformat(row['created_at']),
            parent_version_id=row['parent_version_id'],
            merged_from_version_ids=json.loads(row['merged_from_version_ids']) if row['merged_from_version_ids'] else [],
            is_current=bool(row['is_current']),
            is_published=bool(row['is_published']),
            is_archived=bool(row['is_archived']),
            is_protected=bool(row['is_protected']),
            word_count=row['word_count'],
            tags=json.loads(row['tags']) if row['tags'] else [],
            lines_added=row['lines_added'],
            lines_removed=row['lines_removed'],
            lines_modified=row['lines_modified'],
            content_hash=row['content_hash'],
            content_size=row['content_size'],
            branch_name=row['branch_name'],
            merge_commit_message=row['merge_commit_message'],
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
    
    # Annotation CRUD operations
    def save_annotation(self, annotation: KBAnnotation) -> bool:
        """Save an annotation to the database."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if annotation exists
            cursor.execute("SELECT id FROM kb_annotations WHERE id = ?", (annotation.id,))
            exists = cursor.fetchone() is not None
            
            annotation_data = {
                'id': annotation.id,
                'document_id': annotation.document_id,
                'section_id': annotation.section_id,
                'start_position': annotation.start_position,
                'end_position': annotation.end_position,
                'selected_text': annotation.selected_text,
                'annotation_type': annotation.annotation_type.value,
                'content': annotation.content,
                'title': annotation.title,
                'color': annotation.color,
                'style': annotation.style,
                'priority': annotation.priority.value,
                'created_at': annotation.created_at.isoformat(),
                'updated_at': annotation.updated_at.isoformat(),
                'created_by': annotation.created_by,
                'author_email': annotation.author_email,
                'is_resolved': annotation.is_resolved,
                'is_private': annotation.is_private,
                'is_sticky': annotation.is_sticky,
                'parent_annotation_id': annotation.parent_annotation_id,
                'reply_count': annotation.reply_count,
                'tags': json.dumps(annotation.tags),
                'category': annotation.category,
                'reminder_date': annotation.reminder_date.isoformat() if annotation.reminder_date else None,
                'due_date': annotation.due_date.isoformat() if annotation.due_date else None,
                'shared_with': json.dumps(annotation.shared_with),
                'permissions': json.dumps(annotation.permissions),
                'anchor_context': annotation.anchor_context,
                'xpath': annotation.xpath,
                'custom_metadata': json.dumps(annotation.custom_metadata)
            }
            
            if exists:
                # Update existing annotation
                set_clause = ", ".join([f"{k} = ?" for k in annotation_data.keys() if k != 'id'])
                values = [v for k, v in annotation_data.items() if k != 'id'] + [annotation.id]
                cursor.execute(f"UPDATE kb_annotations SET {set_clause} WHERE id = ?", values)
            else:
                # Insert new annotation
                columns = ", ".join(annotation_data.keys())
                placeholders = ", ".join(["?"] * len(annotation_data))
                cursor.execute(
                    f"INSERT INTO kb_annotations ({columns}) VALUES ({placeholders})",
                    list(annotation_data.values())
                )
                
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving annotation {annotation.id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def get_annotation_by_id(self, annotation_id: str) -> Optional[KBAnnotation]:
        """Get an annotation by ID."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_annotations WHERE id = ?", (annotation_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_annotation(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting annotation {annotation_id}: {e}")
            return None
        finally:
            conn.close()
            
    def get_annotations_by_document(self, document_id: str) -> List[KBAnnotation]:
        """Get all annotations for a document."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_annotations WHERE document_id = ? ORDER BY start_position ASC",
                (document_id,)
            )
            return [self._row_to_annotation(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting annotations for document {document_id}: {e}")
            return []
        finally:
            conn.close()
            
    def _row_to_annotation(self, row: sqlite3.Row) -> KBAnnotation:
        """Convert database row to KBAnnotation object."""
        return KBAnnotation(
            id=row['id'],
            document_id=row['document_id'],
            section_id=row['section_id'],
            start_position=row['start_position'],
            end_position=row['end_position'],
            selected_text=row['selected_text'],
            annotation_type=KBAnnotationType(row['annotation_type']),
            content=row['content'],
            title=row['title'],
            color=row['color'],
            style=row['style'],
            priority=KBAnnotationPriority(row['priority']),
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            created_by=row['created_by'],
            author_email=row['author_email'],
            is_resolved=bool(row['is_resolved']),
            is_private=bool(row['is_private']),
            is_sticky=bool(row['is_sticky']),
            parent_annotation_id=row['parent_annotation_id'],
            reply_count=row['reply_count'],
            tags=json.loads(row['tags']) if row['tags'] else [],
            category=row['category'],
            reminder_date=datetime.fromisoformat(row['reminder_date']) if row['reminder_date'] else None,
            due_date=datetime.fromisoformat(row['due_date']) if row['due_date'] else None,
            shared_with=json.loads(row['shared_with']) if row['shared_with'] else [],
            permissions=json.loads(row['permissions']) if row['permissions'] else {},
            anchor_context=row['anchor_context'],
            xpath=row['xpath'],
            custom_metadata=json.loads(row['custom_metadata']) if row['custom_metadata'] else {}
        )
    
    # Collection management operations
    def get_all_collections(self) -> List[KBCollection]:
        """Get all collections."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM kb_collections ORDER BY path ASC")
            return [self._row_to_collection(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting all collections: {e}")
            return []
        finally:
            conn.close()
            
    def get_root_collections(self) -> List[KBCollection]:
        """Get root-level collections."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_collections WHERE parent_collection_id IS NULL ORDER BY display_order ASC, name ASC"
            )
            return [self._row_to_collection(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting root collections: {e}")
            return []
        finally:
            conn.close()
            
    def get_child_collections(self, parent_id: str) -> List[KBCollection]:
        """Get child collections of a parent."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_collections WHERE parent_collection_id = ? ORDER BY display_order ASC, name ASC",
                (parent_id,)
            )
            return [self._row_to_collection(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting child collections for {parent_id}: {e}")
            return []
        finally:
            conn.close()
            
    def get_documents_by_collection(self, collection_id: str) -> List[KBDocument]:
        """Get all documents in a collection."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT * FROM kb_documents WHERE collection_id = ? ORDER BY created_at DESC",
                (collection_id,)
            )
            return [self._row_to_document(row) for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting documents for collection {collection_id}: {e}")
            return []
        finally:
            conn.close()
            
    def delete_collection(self, collection_id: str) -> bool:
        """Delete a collection and optionally its children."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Delete the collection (CASCADE will handle children)
            cursor.execute("DELETE FROM kb_collections WHERE id = ?", (collection_id,))
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error deleting collection {collection_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
            
    def delete_document(self, document_id: str) -> bool:
        """Delete a document and all related data."""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # Delete the document (CASCADE will handle related data)
            cursor.execute("DELETE FROM kb_documents WHERE id = ?", (document_id,))
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    # Section operations
    def create_section(self, section: 'KBSection') -> str:
        """Create a new section."""
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO kb_sections (
                id, document_id, parent_section_id, order_index, depth_level,
                title, content, section_type, tags, word_count,
                is_visible, is_collapsible, has_images, has_tables, has_formulas,
                custom_metadata, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            section.id, section.document_id, section.parent_section_id,
            section.order_index, section.depth_level, section.title,
            section.content, section.section_type, json.dumps(section.tags),
            section.word_count, section.is_visible, section.is_collapsible,
            section.has_images, section.has_tables, section.has_formulas,
            json.dumps(section.custom_metadata), section.created_at, section.updated_at
        ))
        self.connection.commit()
        return section.id
    
    def get_section(self, section_id: str) -> Optional['KBSection']:
        """Get a section by ID."""
        cursor = self.connection.cursor()
        cursor.execute("SELECT * FROM kb_sections WHERE id = ?", (section_id,))
        row = cursor.fetchone()
        
        if row:
            from ..models.kb_section import KBSection
            return KBSection(
                id=row[0], document_id=row[1], parent_section_id=row[2],
                order_index=row[3], depth_level=row[4], title=row[5],
                content=row[6], section_type=row[7], tags=json.loads(row[8]),
                word_count=row[9], is_visible=bool(row[10]),
                is_collapsible=bool(row[11]), has_images=bool(row[12]),
                has_tables=bool(row[13]), has_formulas=bool(row[14]),
                custom_metadata=json.loads(row[15]),
                created_at=datetime.fromisoformat(row[16]),
                updated_at=datetime.fromisoformat(row[17])
            )
        return None
    
    def update_section(self, section: 'KBSection') -> bool:
        """Update an existing section."""
        section.update_timestamps()
        cursor = self.connection.cursor()
        cursor.execute("""
            UPDATE kb_sections SET
                parent_section_id = ?, order_index = ?, depth_level = ?,
                title = ?, content = ?, section_type = ?, tags = ?,
                word_count = ?, is_visible = ?, is_collapsible = ?,
                has_images = ?, has_tables = ?, has_formulas = ?,
                custom_metadata = ?, updated_at = ?
            WHERE id = ?
        """, (
            section.parent_section_id, section.order_index, section.depth_level,
            section.title, section.content, section.section_type,
            json.dumps(section.tags), section.word_count, section.is_visible,
            section.is_collapsible, section.has_images, section.has_tables,
            section.has_formulas, json.dumps(section.custom_metadata),
            section.updated_at, section.id
        ))
        self.connection.commit()
        return cursor.rowcount > 0
    
    def delete_section(self, section_id: str) -> bool:
        """Delete a section and all its subsections."""
        cursor = self.connection.cursor()
        # First delete all subsections recursively
        self._delete_subsections_recursive(section_id)
        # Then delete the section itself
        cursor.execute("DELETE FROM kb_sections WHERE id = ?", (section_id,))
        self.connection.commit()
        return cursor.rowcount > 0
    
    def _delete_subsections_recursive(self, parent_id: str):
        """Recursively delete all subsections."""
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM kb_sections WHERE parent_section_id = ?", (parent_id,))
        subsections = cursor.fetchall()
        
        for (subsection_id,) in subsections:
            self._delete_subsections_recursive(subsection_id)
            cursor.execute("DELETE FROM kb_sections WHERE id = ?", (subsection_id,))
    
    def get_document_sections(self, document_id: str) -> List['KBSection']:
        """Get all sections for a document ordered by hierarchy."""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM kb_sections 
            WHERE document_id = ? 
            ORDER BY depth_level, order_index
        """, (document_id,))
        
        sections = []
        for row in cursor.fetchall():
            from ..models.kb_section import KBSection
            sections.append(KBSection(
                id=row[0], document_id=row[1], parent_section_id=row[2],
                order_index=row[3], depth_level=row[4], title=row[5],
                content=row[6], section_type=row[7], tags=json.loads(row[8]),
                word_count=row[9], is_visible=bool(row[10]),
                is_collapsible=bool(row[11]), has_images=bool(row[12]),
                has_tables=bool(row[13]), has_formulas=bool(row[14]),
                custom_metadata=json.loads(row[15]),
                created_at=datetime.fromisoformat(row[16]),
                updated_at=datetime.fromisoformat(row[17])
            ))
        return sections
    
    # Reference operations
    def create_reference(self, reference: 'KBReference') -> str:
        """Create a new reference."""
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO kb_references (
                id, source_document_id, target_document_id, source_section_id,
                target_section_id, reference_type, title, description,
                source_text, target_text, page_number, line_number,
                is_bidirectional, strength, context, custom_metadata,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            reference.id, reference.source_document_id, reference.target_document_id,
            reference.source_section_id, reference.target_section_id,
            reference.reference_type, reference.title, reference.description,
            reference.source_text, reference.target_text, reference.page_number,
            reference.line_number, reference.is_bidirectional, reference.strength,
            reference.context, json.dumps(reference.custom_metadata),
            reference.created_at, reference.updated_at
        ))
        self.connection.commit()
        return reference.id
    
    def get_reference(self, reference_id: str) -> Optional['KBReference']:
        """Get a reference by ID."""
        cursor = self.connection.cursor()
        cursor.execute("SELECT * FROM kb_references WHERE id = ?", (reference_id,))
        row = cursor.fetchone()
        
        if row:
            from ..models.kb_reference import KBReference
            return KBReference(
                id=row[0], source_document_id=row[1], target_document_id=row[2],
                source_section_id=row[3], target_section_id=row[4],
                reference_type=row[5], title=row[6], description=row[7],
                source_text=row[8], target_text=row[9], page_number=row[10],
                line_number=row[11], is_bidirectional=bool(row[12]),
                strength=row[13], context=row[14],
                custom_metadata=json.loads(row[15]),
                created_at=datetime.fromisoformat(row[16]),
                updated_at=datetime.fromisoformat(row[17])
            )
        return None
    
    def get_document_references(self, document_id: str) -> List['KBReference']:
        """Get all references for a document (both incoming and outgoing)."""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM kb_references 
            WHERE source_document_id = ? OR target_document_id = ?
            ORDER BY created_at DESC
        """, (document_id, document_id))
        
        references = []
        for row in cursor.fetchall():
            from ..models.kb_reference import KBReference
            references.append(KBReference(
                id=row[0], source_document_id=row[1], target_document_id=row[2],
                source_section_id=row[3], target_section_id=row[4],
                reference_type=row[5], title=row[6], description=row[7],
                source_text=row[8], target_text=row[9], page_number=row[10],
                line_number=row[11], is_bidirectional=bool(row[12]),
                strength=row[13], context=row[14],
                custom_metadata=json.loads(row[15]),
                created_at=datetime.fromisoformat(row[16]),
                updated_at=datetime.fromisoformat(row[17])
            ))
        return references
    
    # Attachment operations
    def create_attachment(self, attachment: 'KBAttachment') -> str:
        """Create a new attachment."""
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO kb_attachments (
                id, document_id, section_id, filename, original_filename,
                file_path, file_size, mime_type, file_hash, attachment_type,
                title, description, is_embedded, thumbnail_path,
                extracted_text, processing_status, custom_metadata,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            attachment.id, attachment.document_id, attachment.section_id,
            attachment.filename, attachment.original_filename, attachment.file_path,
            attachment.file_size, attachment.mime_type, attachment.file_hash,
            attachment.attachment_type, attachment.title, attachment.description,
            attachment.is_embedded, attachment.thumbnail_path,
            attachment.extracted_text, attachment.processing_status,
            json.dumps(attachment.custom_metadata), attachment.created_at,
            attachment.updated_at
        ))
        self.connection.commit()
        return attachment.id
    
    def get_attachment(self, attachment_id: str) -> Optional['KBAttachment']:
        """Get an attachment by ID."""
        cursor = self.connection.cursor()
        cursor.execute("SELECT * FROM kb_attachments WHERE id = ?", (attachment_id,))
        row = cursor.fetchone()
        
        if row:
            from ..models.kb_attachment import KBAttachment
            return KBAttachment(
                id=row[0], document_id=row[1], section_id=row[2],
                filename=row[3], original_filename=row[4], file_path=row[5],
                file_size=row[6], mime_type=row[7], file_hash=row[8],
                attachment_type=row[9], title=row[10], description=row[11],
                is_embedded=bool(row[12]), thumbnail_path=row[13],
                extracted_text=row[14], processing_status=row[15],
                custom_metadata=json.loads(row[16]),
                created_at=datetime.fromisoformat(row[17]),
                updated_at=datetime.fromisoformat(row[18])
            )
        return None
    
    def get_document_attachments(self, document_id: str) -> List['KBAttachment']:
        """Get all attachments for a document."""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM kb_attachments 
            WHERE document_id = ? 
            ORDER BY created_at DESC
        """, (document_id,))
        
        attachments = []
        for row in cursor.fetchall():
            from ..models.kb_attachment import KBAttachment
            attachments.append(KBAttachment(
                id=row[0], document_id=row[1], section_id=row[2],
                filename=row[3], original_filename=row[4], file_path=row[5],
                file_size=row[6], mime_type=row[7], file_hash=row[8],
                attachment_type=row[9], title=row[10], description=row[11],
                is_embedded=bool(row[12]), thumbnail_path=row[13],
                extracted_text=row[14], processing_status=row[15],
                custom_metadata=json.loads(row[16]),
                created_at=datetime.fromisoformat(row[17]),
                updated_at=datetime.fromisoformat(row[18])
            ))
        return attachments
    
    # Version operations
    def create_version(self, version: 'KBVersion') -> str:
        """Create a new version."""
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO kb_versions (
                id, document_id, version_number, parent_version_id,
                title, content, change_summary, change_type,
                author, is_current, is_published, branch_name,
                file_path, file_size, file_hash, custom_metadata,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            version.id, version.document_id, version.version_number,
            version.parent_version_id, version.title, version.content,
            version.change_summary, version.change_type, version.author,
            version.is_current, version.is_published, version.branch_name,
            version.file_path, version.file_size, version.file_hash,
            json.dumps(version.custom_metadata), version.created_at
        ))
        self.connection.commit()
        return version.id
    
    def get_document_versions(self, document_id: str) -> List['KBVersion']:
        """Get all versions for a document."""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM kb_versions 
            WHERE document_id = ? 
            ORDER BY version_number DESC
        """, (document_id,))
        
        versions = []
        for row in cursor.fetchall():
            from ..models.kb_version import KBVersion
            versions.append(KBVersion(
                id=row[0], document_id=row[1], version_number=row[2],
                parent_version_id=row[3], title=row[4], content=row[5],
                change_summary=row[6], change_type=row[7], author=row[8],
                is_current=bool(row[9]), is_published=bool(row[10]),
                branch_name=row[11], file_path=row[12], file_size=row[13],
                file_hash=row[14], custom_metadata=json.loads(row[15]),
                created_at=datetime.fromisoformat(row[16])
            ))
        return versions
    
    # Annotation operations
    def create_annotation(self, annotation: 'KBAnnotation') -> str:
        """Create a new annotation."""
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO kb_annotations (
                id, document_id, section_id, annotation_type, start_position,
                end_position, selected_text, content, title, author,
                color, is_private, parent_annotation_id, tags,
                custom_metadata, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            annotation.id, annotation.document_id, annotation.section_id,
            annotation.annotation_type, annotation.start_position,
            annotation.end_position, annotation.selected_text, annotation.content,
            annotation.title, annotation.author, annotation.color,
            annotation.is_private, annotation.parent_annotation_id,
            json.dumps(annotation.tags), json.dumps(annotation.custom_metadata),
            annotation.created_at, annotation.updated_at
        ))
        self.connection.commit()
        return annotation.id
    
    def get_document_annotations(self, document_id: str) -> List['KBAnnotation']:
        """Get all annotations for a document."""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM kb_annotations 
            WHERE document_id = ? 
            ORDER BY start_position
        """, (document_id,))
        
        annotations = []
        for row in cursor.fetchall():
            from ..models.kb_annotation import KBAnnotation
            annotations.append(KBAnnotation(
                id=row[0], document_id=row[1], section_id=row[2],
                annotation_type=row[3], start_position=row[4],
                end_position=row[5], selected_text=row[6], content=row[7],
                title=row[8], author=row[9], color=row[10],
                is_private=bool(row[11]), parent_annotation_id=row[12],
                tags=json.loads(row[13]), custom_metadata=json.loads(row[14]),
                created_at=datetime.fromisoformat(row[15]),
                updated_at=datetime.fromisoformat(row[16])
            ))
        return annotations


# Export the main repository class
__all__ = ['KnowledgeBaseRepository']
