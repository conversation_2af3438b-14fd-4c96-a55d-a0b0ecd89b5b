"""
Knowledge Base Management System

This module provides comprehensive knowledge base functionality including:
- Advanced document storage and retrieval
- Rich text content with formatting, images, and tables
- Full-text search with relevance ranking
- Document relationships and cross-references
- Version control and change tracking
- Integration with RTF editor
"""

from knowledge_base.models.kb_document import KBDocument, KBDocumentType, KBDocumentStatus
from knowledge_base.models.kb_section import KBSection
from knowledge_base.models.kb_reference import KBReference, KBReferenceType
from knowledge_base.models.kb_attachment import KBAttachment, KBAttachmentType
from knowledge_base.models.kb_version import KBVersion
from knowledge_base.models.kb_collection import KBCollection
from knowledge_base.models.kb_annotation import KBAnnotation, KBAnnotationType
from knowledge_base.repositories import KnowledgeBaseRepository
from knowledge_base.services.knowledge_base_service import KnowledgeBaseService

__all__ = [
    "KBDocument",
    "KBDocumentType", 
    "KBDocumentStatus",
    "KBSection",
    "KBReference",
    "KBReferenceType",
    "KBAttachment",
    "KBAttachmentType", 
    "KBVersion",
    "KBCollection",
    "KBAnnotation",
    "KBAnnotationType",
    "KnowledgeBaseRepository",
    "KnowledgeBaseService",
]
